{"logs": [{"outputFile": "com.visuallabstudio.ide.app-mergeDebugResources-53:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8894ffd5200146a245d974f5f73061d6\\transformed\\core-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "525,618,718,815,914,1010,1112,5968", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "613,713,810,909,1005,1107,1207,6064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3fdae449ec0975b3ed67992fb9c55c3c\\transformed\\jetified-ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,922,988,1066,1147,1217,1297,1362", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,917,983,1061,1142,1212,1292,1357,1473"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1212,1295,4711,4803,4969,5125,5203,5286,5368,5446,5512,5578,5656,5817,6167,6247,6312", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "1290,1367,4798,4894,5046,5198,5281,5363,5441,5507,5573,5651,5732,5882,6242,6307,6423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f063da582707269a21bd980d1f8ebd44\\transformed\\jetified-material3-1.1.2\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,268,372,475,550,636,740,864,970,1095,1173,1267,1350,1439,1545,1660,1759,1878,1999,2118,2262,2373,2481,2591,2702,2786,2877,2983,3098,3189,3287,3385,3500,3622,3723,3814,3884,3958,4038,4119,4217,4294,4373,4469,4562,4653,4744,4825,4922,5017,5111,5226,5304,5401", "endColumns": "106,105,103,102,74,85,103,123,105,124,77,93,82,88,105,114,98,118,120,118,143,110,107,109,110,83,90,105,114,90,97,97,114,121,100,90,69,73,79,80,97,76,78,95,92,90,90,80,96,94,93,114,77,96,92", "endOffsets": "157,263,367,470,545,631,735,859,965,1090,1168,1262,1345,1434,1540,1655,1754,1873,1994,2113,2257,2368,2476,2586,2697,2781,2872,2978,3093,3184,3282,3380,3495,3617,3718,3809,3879,3953,4033,4114,4212,4289,4368,4464,4557,4648,4739,4820,4917,5012,5106,5221,5299,5396,5489"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,422,1372,1447,1533,1637,1761,1867,1992,2070,2164,2247,2336,2442,2557,2656,2775,2896,3015,3159,3270,3378,3488,3599,3683,3774,3880,3995,4086,4184,4282,4397,4519,4620,4899,5051,5737,5887,6069,6428,6505,6584,6680,6773,6864,6955,7036,7133,7228,7322,7437,7515,7612", "endColumns": "106,105,103,102,74,85,103,123,105,124,77,93,82,88,105,114,98,118,120,118,143,110,107,109,110,83,90,105,114,90,97,97,114,121,100,90,69,73,79,80,97,76,78,95,92,90,90,80,96,94,93,114,77,96,92", "endOffsets": "207,313,417,520,1442,1528,1632,1756,1862,1987,2065,2159,2242,2331,2437,2552,2651,2770,2891,3010,3154,3265,3373,3483,3594,3678,3769,3875,3990,4081,4179,4277,4392,4514,4615,4706,4964,5120,5812,5963,6162,6500,6579,6675,6768,6859,6950,7031,7128,7223,7317,7432,7510,7607,7700"}}]}]}