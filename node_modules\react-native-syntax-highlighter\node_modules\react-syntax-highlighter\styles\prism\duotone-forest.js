"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = {
    "code[class*=\"language-\"]": {
        "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
        "fontSize": "14px",
        "lineHeight": "1.375",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "background": "#2a2d2a",
        "color": "#687d68"
    },
    "pre[class*=\"language-\"]": {
        "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
        "fontSize": "14px",
        "lineHeight": "1.375",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "background": "#2a2d2a",
        "color": "#687d68",
        "padding": "1em",
        "margin": ".5em 0",
        "overflow": "auto"
    },
    "pre[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "#435643"
    },
    "pre[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "#435643"
    },
    "code[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "#435643"
    },
    "code[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "#435643"
    },
    "pre[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "#435643"
    },
    "pre[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "#435643"
    },
    "code[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "#435643"
    },
    "code[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "#435643"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "padding": ".1em",
        "borderRadius": ".3em"
    },
    "comment": {
        "color": "#535f53"
    },
    "prolog": {
        "color": "#535f53"
    },
    "doctype": {
        "color": "#535f53"
    },
    "cdata": {
        "color": "#535f53"
    },
    "punctuation": {
        "color": "#535f53"
    },
    "namespace": {
        "Opacity": ".7"
    },
    "tag": {
        "color": "#a2b34d"
    },
    "operator": {
        "color": "#a2b34d"
    },
    "number": {
        "color": "#a2b34d"
    },
    "property": {
        "color": "#687d68"
    },
    "function": {
        "color": "#687d68"
    },
    "tag-id": {
        "color": "#f0fff0"
    },
    "selector": {
        "color": "#f0fff0"
    },
    "atrule-id": {
        "color": "#f0fff0"
    },
    "code.language-javascript": {
        "color": "#b3d6b3"
    },
    "attr-name": {
        "color": "#b3d6b3"
    },
    "code.language-css": {
        "color": "#e5fb79"
    },
    "code.language-scss": {
        "color": "#e5fb79"
    },
    "boolean": {
        "color": "#e5fb79"
    },
    "string": {
        "color": "#e5fb79"
    },
    "entity": {
        "color": "#e5fb79",
        "cursor": "help"
    },
    "url": {
        "color": "#e5fb79"
    },
    ".language-css .token.string": {
        "color": "#e5fb79"
    },
    ".language-scss .token.string": {
        "color": "#e5fb79"
    },
    ".style .token.string": {
        "color": "#e5fb79"
    },
    "attr-value": {
        "color": "#e5fb79"
    },
    "keyword": {
        "color": "#e5fb79"
    },
    "control": {
        "color": "#e5fb79"
    },
    "directive": {
        "color": "#e5fb79"
    },
    "unit": {
        "color": "#e5fb79"
    },
    "statement": {
        "color": "#e5fb79"
    },
    "regex": {
        "color": "#e5fb79"
    },
    "atrule": {
        "color": "#e5fb79"
    },
    "placeholder": {
        "color": "#e5fb79"
    },
    "variable": {
        "color": "#e5fb79"
    },
    "deleted": {
        "textDecoration": "line-through"
    },
    "inserted": {
        "borderBottom": "1px dotted #f0fff0",
        "textDecoration": "none"
    },
    "italic": {
        "fontStyle": "italic"
    },
    "important": {
        "fontWeight": "bold",
        "color": "#b3d6b3"
    },
    "bold": {
        "fontWeight": "bold"
    },
    "pre > code.highlight": {
        "Outline": ".4em solid #5c705c",
        "OutlineOffset": ".4em"
    },
    ".line-numbers .line-numbers-rows": {
        "borderRightColor": "#2c302c"
    },
    ".line-numbers-rows > span:before": {
        "color": "#3b423b"
    },
    ".line-highlight": {
        "background": "linear-gradient(to right, rgba(162, 179, 77, 0.2) 70%, rgba(162, 179, 77, 0))"
    }
};