@echo off
echo ========================================
echo Fixing File Encoding Issues
echo ========================================

echo [1/3] Stopping Metro...
taskkill /f /im node.exe 2>nul

echo [2/3] Cleaning Metro cache...
rmdir /s /q %TEMP%\metro-* 2>nul
rmdir /s /q %TEMP%\react-* 2>nul

echo [3/3] Installing missing dependencies...
call npm install @react-navigation/native @react-navigation/bottom-tabs
call npm install react-native-screens react-native-safe-area-context
call npm install react-native-vector-icons

echo ========================================
echo ✅ Fix complete! 
echo ========================================
echo Now run: npm start --reset-cache
pause