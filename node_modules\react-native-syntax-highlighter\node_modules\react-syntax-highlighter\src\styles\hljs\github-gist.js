export default {
    "hljs": {
        "display": "block",
        "background": "white",
        "padding": "0.5em",
        "color": "#333333",
        "overflowX": "auto"
    },
    "hljs-comment": {
        "color": "#969896"
    },
    "hljs-meta": {
        "color": "#969896"
    },
    "hljs-string": {
        "color": "#df5000"
    },
    "hljs-variable": {
        "color": "#df5000"
    },
    "hljs-template-variable": {
        "color": "#df5000"
    },
    "hljs-strong": {
        "color": "#df5000"
    },
    "hljs-emphasis": {
        "color": "#df5000"
    },
    "hljs-quote": {
        "color": "#df5000"
    },
    "hljs-keyword": {
        "color": "#a71d5d"
    },
    "hljs-selector-tag": {
        "color": "#a71d5d"
    },
    "hljs-type": {
        "color": "#a71d5d"
    },
    "hljs-literal": {
        "color": "#0086b3"
    },
    "hljs-symbol": {
        "color": "#0086b3"
    },
    "hljs-bullet": {
        "color": "#0086b3"
    },
    "hljs-attribute": {
        "color": "#0086b3"
    },
    "hljs-section": {
        "color": "#63a35c"
    },
    "hljs-name": {
        "color": "#63a35c"
    },
    "hljs-tag": {
        "color": "#333333"
    },
    "hljs-title": {
        "color": "#795da3"
    },
    "hljs-attr": {
        "color": "#795da3"
    },
    "hljs-selector-id": {
        "color": "#795da3"
    },
    "hljs-selector-class": {
        "color": "#795da3"
    },
    "hljs-selector-attr": {
        "color": "#795da3"
    },
    "hljs-selector-pseudo": {
        "color": "#795da3"
    },
    "hljs-addition": {
        "color": "#55a532",
        "backgroundColor": "#eaffea"
    },
    "hljs-deletion": {
        "color": "#bd2c00",
        "backgroundColor": "#ffecec"
    },
    "hljs-link": {
        "textDecoration": "underline"
    }
}