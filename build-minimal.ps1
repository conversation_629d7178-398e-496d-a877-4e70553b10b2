#!/usr/bin/env pwsh

Write-Host "========================================" -ForegroundColor Green
Write-Host "Building Minimal Visual Lab Studio APK" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Set Android environment variables
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:ANDROID_SDK_ROOT = "C:\Users\<USER>\AppData\Local\Android\Sdk"

Write-Host "[1/6] Setting up environment..." -ForegroundColor Yellow
Write-Host "ANDROID_HOME: $env:ANDROID_HOME" -ForegroundColor Cyan

Write-Host "[2/6] Creating minimal project structure..." -ForegroundColor Yellow
$minimalPath = "C:\VLS\Minimal"
if (Test-Path $minimalPath) {
    Remove-Item $minimalPath -Recurse -Force
}
New-Item -ItemType Directory -Path $minimalPath -Force | Out-Null

Write-Host "[3/6] Initializing minimal React Native project..." -ForegroundColor Yellow
Set-Location $minimalPath
npx react-native@latest init VisualLabStudioMinimal --skip-install

Write-Host "[4/6] Copying essential source files..." -ForegroundColor Yellow
Set-Location VisualLabStudioMinimal

# Copy only essential files
Copy-Item "C:\VLS\VisualLabStudio\src\contexts\ThemeContext.tsx" "src\contexts\" -Force -Recurse -ErrorAction SilentlyContinue
Copy-Item "C:\VLS\VisualLabStudio\src\types\index.ts" "src\types\" -Force -Recurse -ErrorAction SilentlyContinue

# Create minimal App.tsx
@"
import React from 'react';
import { View, Text, StyleSheet, StatusBar } from 'react-native';

function App() {
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      <Text style={styles.title}>Visual Lab Studio IDE</Text>
      <Text style={styles.subtitle}>Mobile Development Environment</Text>
      <Text style={styles.version}>Version 1.0.0</Text>
      <Text style={styles.description}>
        A powerful mobile IDE built with React Native
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 20,
  },
  version: {
    fontSize: 16,
    color: '#0A84FF',
    textAlign: 'center',
    marginBottom: 30,
  },
  description: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default App;
"@ | Set-Content -Path "App.tsx" -Encoding UTF8

Write-Host "[5/6] Installing minimal dependencies..." -ForegroundColor Yellow
npm install

Write-Host "[6/6] Building APK..." -ForegroundColor Yellow
Set-Location android

# Build APK
.\gradlew assembleDebug

if ($LASTEXITCODE -eq 0) {
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "BUILD SUCCESSFUL!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    
    $apkPath = "app\build\outputs\apk\debug\app-debug.apk"
    if (Test-Path $apkPath) {
        $fullPath = Resolve-Path $apkPath
        Write-Host "APK Location: $fullPath" -ForegroundColor Cyan
        
        # Copy APK to original directory
        Copy-Item $apkPath "C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\VisualLabStudio-minimal.apk" -Force
        Write-Host "APK copied to original directory: VisualLabStudio-minimal.apk" -ForegroundColor Cyan
        
        # Show APK info
        $apkSize = (Get-Item $apkPath).Length / 1MB
        Write-Host "APK Size: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Cyan
        
        Write-Host "" -ForegroundColor Green
        Write-Host "🎉 SUCCESS! Minimal APK generated successfully!" -ForegroundColor Green
        Write-Host "📱 Install the APK on your Android device to test." -ForegroundColor Green
        
    } else {
        Write-Host "WARNING: APK file not found at expected location" -ForegroundColor Yellow
    }
} else {
    Write-Host "BUILD FAILED!" -ForegroundColor Red
    exit 1
}

Write-Host "Build process completed!" -ForegroundColor Green
