import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '../contexts/ThemeContext';
import { FileEntity, RecentFileEntity } from '../types';

interface TabButtonProps {
  title: string;
  isActive: boolean;
  onPress: () => void;
}

const TabButton: React.FC<TabButtonProps> = ({ title, isActive, onPress }) => {
  const { theme } = useTheme();
  
  return (
    <TouchableOpacity
      style={[
        styles.tabButton,
        {
          backgroundColor: isActive ? theme.colors.primary : 'transparent',
          borderBottomColor: isActive ? theme.colors.primary : 'transparent',
        }
      ]}
      onPress={onPress}
    >
      <Text
        style={[
          styles.tabButtonText,
          {
            color: isActive ? theme.colors.background : theme.colors.text,
            fontWeight: isActive ? '600' : '400',
          }
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
};

interface FileItemProps {
  file: FileEntity;
  onPress: () => void;
  onDelete: () => void;
}

const FileItem: React.FC<FileItemProps> = ({ file, onPress, onDelete }) => {
  const { theme } = useTheme();
  
  const getFileIcon = (language: string) => {
    switch (language.toLowerCase()) {
      case 'javascript':
        return 'code';
      case 'python':
        return 'code';
      case 'html':
        return 'web';
      case 'css':
        return 'palette';
      case 'json':
        return 'data-object';
      case 'markdown':
        return 'description';
      default:
        return 'insert-drive-file';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <TouchableOpacity
      style={[styles.fileItem, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
      onPress={onPress}
    >
      <View style={styles.fileIcon}>
        <Icon name={getFileIcon(file.language)} size={24} color={theme.colors.primary} />
      </View>
      
      <View style={styles.fileInfo}>
        <Text style={[styles.fileName, { color: theme.colors.text }]} numberOfLines={1}>
          {file.name}
        </Text>
        <Text style={[styles.fileDetails, { color: theme.colors.textSecondary }]} numberOfLines={1}>
          {file.language.toUpperCase()} • {formatFileSize(file.size)} • {formatDate(file.lastModified)}
        </Text>
        {file.path && (
          <Text style={[styles.filePath, { color: theme.colors.textSecondary }]} numberOfLines={1}>
            {file.path}
          </Text>
        )}
      </View>
      
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={onDelete}
      >
        <Icon name="delete" size={20} color={theme.colors.error} />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

interface RecentFileItemProps {
  recentFile: RecentFileEntity;
  onPress: () => void;
}

const RecentFileItem: React.FC<RecentFileItemProps> = ({ recentFile, onPress }) => {
  const { theme } = useTheme();
  
  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (hours < 1) return 'Just now';
    if (hours < 24) return `${hours}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <TouchableOpacity
      style={[styles.recentFileItem, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
      onPress={onPress}
    >
      <View style={styles.fileIcon}>
        <Icon name="history" size={24} color={theme.colors.accent} />
      </View>
      
      <View style={styles.fileInfo}>
        <Text style={[styles.fileName, { color: theme.colors.text }]} numberOfLines={1}>
          {recentFile.fileName}
        </Text>
        <Text style={[styles.fileDetails, { color: theme.colors.textSecondary }]} numberOfLines={1}>
          Opened {formatDate(recentFile.lastOpened)}
        </Text>
        {recentFile.filePath && (
          <Text style={[styles.filePath, { color: theme.colors.textSecondary }]} numberOfLines={1}>
            {recentFile.filePath}
          </Text>
        )}
      </View>
      
      <Icon name="chevron-right" size={20} color={theme.colors.textSecondary} />
    </TouchableOpacity>
  );
};

const ExplorerScreen: React.FC = () => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState<'recent' | 'files'>('recent');
  const [recentFiles, setRecentFiles] = useState<RecentFileEntity[]>([]);
  const [allFiles, setAllFiles] = useState<FileEntity[]>([]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    // Mock data - in real app, this would come from database
    const mockRecentFiles: RecentFileEntity[] = [
      {
        id: '1',
        fileId: 'file-1',
        fileName: 'hello_world.js',
        filePath: '/examples/hello_world.js',
        lastOpened: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      },
      {
        id: '2',
        fileId: 'file-2',
        fileName: 'app.py',
        filePath: '/projects/my-app/app.py',
        lastOpened: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      },
    ];

    const mockFiles: FileEntity[] = [
      {
        id: 'file-1',
        name: 'hello_world.js',
        path: '/examples/hello_world.js',
        content: 'console.log("Hello World!");',
        language: 'javascript',
        size: 1024,
        lastModified: new Date(),
        isTemporary: false,
      },
      {
        id: 'file-2',
        name: 'app.py',
        path: '/projects/my-app/app.py',
        content: 'print("Hello from Python!")',
        language: 'python',
        size: 2048,
        lastModified: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        isTemporary: false,
      },
      {
        id: 'file-3',
        name: 'styles.css',
        path: '/projects/web-app/styles.css',
        content: 'body { margin: 0; }',
        language: 'css',
        size: 512,
        lastModified: new Date(Date.now() - 1000 * 60 * 60 * 48), // 2 days ago
        isTemporary: false,
      },
    ];

    setRecentFiles(mockRecentFiles);
    setAllFiles(mockFiles);
  };

  const handleFilePress = (file: FileEntity) => {
    Alert.alert('Open File', `Opening ${file.name}...`);
    // TODO: Implement file opening logic
  };

  const handleRecentFilePress = (recentFile: RecentFileEntity) => {
    Alert.alert('Open Recent File', `Opening ${recentFile.fileName}...`);
    // TODO: Implement recent file opening logic
  };

  const handleDeleteFile = (fileId: string) => {
    Alert.alert(
      'Delete File',
      'Are you sure you want to delete this file?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setAllFiles(prev => prev.filter(f => f.id !== fileId));
            Alert.alert('Success', 'File deleted successfully');
          },
        },
      ]
    );
  };

  const renderRecentFile = ({ item }: { item: RecentFileEntity }) => (
    <RecentFileItem
      recentFile={item}
      onPress={() => handleRecentFilePress(item)}
    />
  );

  const renderFile = ({ item }: { item: FileEntity }) => (
    <FileItem
      file={item}
      onPress={() => handleFilePress(item)}
      onDelete={() => handleDeleteFile(item.id)}
    />
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <TabButton
          title="Recent"
          isActive={activeTab === 'recent'}
          onPress={() => setActiveTab('recent')}
        />
        <TabButton
          title="All Files"
          isActive={activeTab === 'files'}
          onPress={() => setActiveTab('files')}
        />
      </View>

      {/* Content */}
      <View style={styles.content}>
        {activeTab === 'recent' ? (
          <FlatList
            data={recentFiles}
            renderItem={renderRecentFile}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Icon name="history" size={48} color={theme.colors.textSecondary} />
                <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                  No recent files
                </Text>
              </View>
            }
          />
        ) : (
          <FlatList
            data={allFiles}
            renderItem={renderFile}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Icon name="folder-open" size={48} color={theme.colors.textSecondary} />
                <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                  No files found
                </Text>
              </View>
            }
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  tabButtonText: {
    fontSize: 16,
  },
  content: {
    flex: 1,
  },
  listContainer: {
    padding: 16,
  },
  fileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  recentFileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  fileIcon: {
    marginRight: 12,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  fileDetails: {
    fontSize: 12,
    marginBottom: 2,
  },
  filePath: {
    fontSize: 11,
  },
  deleteButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 16,
  },
});

export default ExplorerScreen;
