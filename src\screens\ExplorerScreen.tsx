import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  RefreshControl,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useFileSystem } from '../utils/useFileSystem';
import { FileEntity, RecentFileEntity } from '../types';

interface TabButtonProps {
  title: string;
  isActive: boolean;
  onPress: () => void;
}

const TabButton: React.FC<TabButtonProps> = ({ title, isActive, onPress }) => {
  const { theme } = useTheme();
  
  return (
    <TouchableOpacity
      style={[
        styles.tabButton,
        {
          backgroundColor: isActive ? theme.colors.primary : 'transparent',
          borderBottomColor: isActive ? theme.colors.primary : 'transparent',
        }
      ]}
      onPress={onPress}
    >
      <Text
        style={[
          styles.tabButtonText,
          {
            color: isActive ? theme.colors.background : theme.colors.text,
            fontWeight: isActive ? '600' : '400',
          }
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
};

interface FileItemProps {
  file: FileEntity;
  onPress: () => void;
  onDelete: () => void;
}

const FileItem: React.FC<FileItemProps> = ({ file, onPress, onDelete }) => {
  const { theme } = useTheme();
  
  const getFileIcon = (language: string) => {
    switch (language.toLowerCase()) {
      case 'javascript':
        return '📄';
      case 'python':
        return '🐍';
      case 'html':
        return '🌐';
      case 'css':
        return '🎨';
      case 'json':
        return '📋';
      case 'markdown':
        return '📝';
      default:
        return '📄';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <TouchableOpacity
      style={[styles.fileItem, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
      onPress={onPress}
    >
      <View style={styles.fileIcon}>
        <Text style={styles.fileIconText}>{getFileIcon(file.language)}</Text>
      </View>
      
      <View style={styles.fileInfo}>
        <Text style={[styles.fileName, { color: theme.colors.text }]} numberOfLines={1}>
          {file.name}
          {file.isTemporary && <Text style={{ color: theme.colors.warning }}> (temp)</Text>}
        </Text>
        <Text style={[styles.fileDetails, { color: theme.colors.textSecondary }]} numberOfLines={1}>
          {file.language.toUpperCase()} • {formatFileSize(file.size)} • {formatDate(file.lastModified)}
        </Text>
        {file.path && (
          <Text style={[styles.filePath, { color: theme.colors.textSecondary }]} numberOfLines={1}>
            {file.path}
          </Text>
        )}
      </View>
      
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={onDelete}
      >
        <Text style={[styles.deleteButtonText, { color: theme.colors.error }]}>×</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

interface RecentFileItemProps {
  recentFile: RecentFileEntity;
  onPress: () => void;
}

const RecentFileItem: React.FC<RecentFileItemProps> = ({ recentFile, onPress }) => {
  const { theme } = useTheme();
  
  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (hours < 1) return 'Just now';
    if (hours < 24) return `${hours}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <TouchableOpacity
      style={[styles.recentFileItem, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
      onPress={onPress}
    >
      <View style={styles.fileIcon}>
        <Text style={styles.fileIconText}>📄</Text>
      </View>
      
      <View style={styles.fileInfo}>
        <Text style={[styles.fileName, { color: theme.colors.text }]} numberOfLines={1}>
          {recentFile.fileName}
        </Text>
        <Text style={[styles.fileDetails, { color: theme.colors.textSecondary }]} numberOfLines={1}>
          Opened {formatDate(recentFile.lastOpened)}
        </Text>
        {recentFile.filePath && (
          <Text style={[styles.filePath, { color: theme.colors.textSecondary }]} numberOfLines={1}>
            {recentFile.filePath}
          </Text>
        )}
      </View>
      
      <Text style={[styles.chevron, { color: theme.colors.textSecondary }]}>›</Text>
    </TouchableOpacity>
  );
};

const ExplorerScreen: React.FC = () => {
  const { theme } = useTheme();
  const {
    files,
    recentFiles,
    loadFile,
    deleteFile,
    refreshFiles,
    refreshRecentFiles,
    isLoading,
    error,
    clearError,
  } = useFileSystem();

  const [activeTab, setActiveTab] = useState<'recent' | 'files'>('recent');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
      clearError();
    }
  }, [error, clearError]);

  const loadData = async () => {
    await refreshFiles();
    await refreshRecentFiles();
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleFilePress = async (file: FileEntity) => {
    await loadFile(file.id);
  };

  const handleRecentFilePress = async (recentFile: RecentFileEntity) => {
    await loadFile(recentFile.fileId);
  };

  const handleDeleteFile = (fileId: string) => {
    Alert.alert(
      'Delete File',
      'Are you sure you want to delete this file?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteFile(fileId);
            if (success) {
              Alert.alert('Success', 'File deleted successfully');
            }
          },
        },
      ]
    );
  };

  const renderRecentFile = ({ item }: { item: RecentFileEntity }) => (
    <RecentFileItem
      recentFile={item}
      onPress={() => handleRecentFilePress(item)}
    />
  );

  const renderFile = ({ item }: { item: FileEntity }) => (
    <FileItem
      file={item}
      onPress={() => handleFilePress(item)}
      onDelete={() => handleDeleteFile(item.id)}
    />
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <TabButton
          title="Recent"
          isActive={activeTab === 'recent'}
          onPress={() => setActiveTab('recent')}
        />
        <TabButton
          title="All Files"
          isActive={activeTab === 'files'}
          onPress={() => setActiveTab('files')}
        />
      </View>

      {/* Content */}
      <View style={styles.content}>
        {activeTab === 'recent' ? (
          <FlatList
            data={recentFiles}
            renderItem={renderRecentFile}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                tintColor={theme.colors.primary}
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyIcon}>📂</Text>
                <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                  No recent files
                </Text>
              </View>
            }
          />
        ) : (
          <FlatList
            data={files}
            renderItem={renderFile}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                tintColor={theme.colors.primary}
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyIcon}>📁</Text>
                <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                  No files found
                </Text>
              </View>
            }
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  tabButtonText: {
    fontSize: 16,
  },
  content: {
    flex: 1,
  },
  listContainer: {
    padding: 16,
  },
  fileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  recentFileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  fileIcon: {
    marginRight: 12,
  },
  fileIconText: {
    fontSize: 24,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  fileDetails: {
    fontSize: 12,
    marginBottom: 2,
  },
  filePath: {
    fontSize: 11,
  },
  deleteButton: {
    padding: 8,
  },
  deleteButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  chevron: {
    fontSize: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
  },
});

export default ExplorerScreen;
