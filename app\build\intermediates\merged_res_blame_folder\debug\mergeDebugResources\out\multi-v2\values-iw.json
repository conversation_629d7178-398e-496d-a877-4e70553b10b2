{"logs": [{"outputFile": "com.visuallabstudio.ide.app-mergeDebugResources-53:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3fdae449ec0975b3ed67992fb9c55c3c\\transformed\\jetified-ui-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,278,371,466,549,626,711,797,876,942,1008,1086,1168,1237,1311,1382", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "189,273,366,461,544,621,706,792,871,937,1003,1081,1163,1232,1306,1377,1496"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1252,1341,4901,4994,5166,5322,5399,5484,5570,5649,5715,5781,5859,6023,6373,6447,6518", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "1336,1420,4989,5084,5244,5394,5479,5565,5644,5710,5776,5854,5936,6087,6442,6513,6632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8894ffd5200146a245d974f5f73061d6\\transformed\\core-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "555,649,751,848,945,1046,1146,6173", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "644,746,843,940,1041,1141,1247,6269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f063da582707269a21bd980d1f8ebd44\\transformed\\jetified-material3-1.1.2\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,280,394,505,579,670,780,914,1025,1161,1242,1336,1423,1515,1628,1744,1843,1975,2106,2223,2372,2486,2592,2704,2819,2907,3000,3108,3228,3322,3417,3517,3646,3783,3886,3981,4058,4131,4213,4294,4393,4469,4549,4646,4741,4828,4919,5001,5099,5194,5286,5407,5483,5580", "endColumns": "112,111,113,110,73,90,109,133,110,135,80,93,86,91,112,115,98,131,130,116,148,113,105,111,114,87,92,107,119,93,94,99,128,136,102,94,76,72,81,80,98,75,79,96,94,86,90,81,97,94,91,120,75,96,91", "endOffsets": "163,275,389,500,574,665,775,909,1020,1156,1237,1331,1418,1510,1623,1739,1838,1970,2101,2218,2367,2481,2587,2699,2814,2902,2995,3103,3223,3317,3412,3512,3641,3778,3881,3976,4053,4126,4208,4289,4388,4464,4544,4641,4736,4823,4914,4996,5094,5189,5281,5402,5478,5575,5667"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,330,444,1425,1499,1590,1700,1834,1945,2081,2162,2256,2343,2435,2548,2664,2763,2895,3026,3143,3292,3406,3512,3624,3739,3827,3920,4028,4148,4242,4337,4437,4566,4703,4806,5089,5249,5941,6092,6274,6637,6713,6793,6890,6985,7072,7163,7245,7343,7438,7530,7651,7727,7824", "endColumns": "112,111,113,110,73,90,109,133,110,135,80,93,86,91,112,115,98,131,130,116,148,113,105,111,114,87,92,107,119,93,94,99,128,136,102,94,76,72,81,80,98,75,79,96,94,86,90,81,97,94,91,120,75,96,91", "endOffsets": "213,325,439,550,1494,1585,1695,1829,1940,2076,2157,2251,2338,2430,2543,2659,2758,2890,3021,3138,3287,3401,3507,3619,3734,3822,3915,4023,4143,4237,4332,4432,4561,4698,4801,4896,5161,5317,6018,6168,6368,6708,6788,6885,6980,7067,7158,7240,7338,7433,7525,7646,7722,7819,7911"}}]}]}