plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'com.chaquo.python'
}

android {
    namespace 'com.visuallabstudio.ide'
    compileSdk 34

    defaultConfig {
        applicationId "com.visuallabstudio.ide"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }

        ndk {
            abiFilters "arm64-v8a", "x86_64"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
        freeCompilerArgs += ["-opt-in=kotlin.RequiresOptIn", "-Xsuppress-version-warnings"]
    }
    
    configurations.all {
        resolutionStrategy {
            force 'org.jetbrains.kotlin:kotlin-stdlib:1.9.20'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20'
        }
    }
    
    buildFeatures {
        compose true
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion compose_version
        kotlinCompilerVersion kotlin_version
    }
    
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

chaquopy {
    defaultConfig {
        version "3.8"
    }
    productFlavors {
        // Necessário mesmo que vazio
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'
    
    // Jetpack Compose
    implementation "androidx.compose.ui:ui:$compose_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    implementation 'androidx.compose.material3:material3:1.1.2'
    implementation "androidx.compose.animation:animation:$compose_version"
    implementation "androidx.compose.foundation:foundation:$compose_version"
    
    // Navigation
    implementation "androidx.navigation:navigation-compose:2.7.6"

    // ViewModel
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0"

    // Room database
    implementation "androidx.room:room-runtime:$room_version"
    implementation "androidx.room:room-ktx:$room_version"
    kapt "androidx.room:room-compiler:$room_version"
    
    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0'
    
    // Storage Access Framework
    implementation 'androidx.documentfile:documentfile:1.0.1'
    
    // JavaScript engine (Rhino)
    implementation 'org.mozilla:rhino:1.7.14'
    
    // Permissions
    implementation 'com.google.accompanist:accompanist-permissions:0.32.0'

    // Pager for navigation
    implementation 'com.google.accompanist:accompanist-pager:0.32.0'
    implementation 'com.google.accompanist:accompanist-pager-indicators:0.32.0'
    
    // Icons
    implementation "androidx.compose.material:material-icons-extended:$compose_version"
    
    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_version"
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_version"
    debugImplementation "androidx.compose.ui:ui-test-manifest:$compose_version"
}
