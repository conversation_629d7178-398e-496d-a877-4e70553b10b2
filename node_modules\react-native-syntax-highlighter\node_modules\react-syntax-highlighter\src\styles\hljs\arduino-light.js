export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#FFFFFF",
        "color": "#434f54"
    },
    "hljs-subst": {
        "color": "#434f54"
    },
    "hljs-keyword": {
        "color": "#00979D"
    },
    "hljs-attribute": {
        "color": "#00979D"
    },
    "hljs-selector-tag": {
        "color": "#00979D"
    },
    "hljs-doctag": {
        "color": "#00979D"
    },
    "hljs-name": {
        "color": "#00979D"
    },
    "hljs-built_in": {
        "color": "#D35400"
    },
    "hljs-literal": {
        "color": "#D35400"
    },
    "hljs-bullet": {
        "color": "#D35400"
    },
    "hljs-code": {
        "color": "#D35400"
    },
    "hljs-addition": {
        "color": "#D35400"
    },
    "hljs-regexp": {
        "color": "#00979D"
    },
    "hljs-symbol": {
        "color": "#00979D"
    },
    "hljs-variable": {
        "color": "#00979D"
    },
    "hljs-template-variable": {
        "color": "#00979D"
    },
    "hljs-link": {
        "color": "#00979D"
    },
    "hljs-selector-attr": {
        "color": "#00979D"
    },
    "hljs-selector-pseudo": {
        "color": "#00979D"
    },
    "hljs-type": {
        "color": "#005C5F"
    },
    "hljs-string": {
        "color": "#005C5F"
    },
    "hljs-selector-id": {
        "color": "#005C5F"
    },
    "hljs-selector-class": {
        "color": "#005C5F"
    },
    "hljs-quote": {
        "color": "#005C5F"
    },
    "hljs-template-tag": {
        "color": "#005C5F"
    },
    "hljs-deletion": {
        "color": "#005C5F"
    },
    "hljs-title": {
        "color": "#880000",
        "fontWeight": "bold"
    },
    "hljs-section": {
        "color": "#880000",
        "fontWeight": "bold"
    },
    "hljs-comment": {
        "color": "rgba(149,165,166,.8)"
    },
    "hljs-meta-keyword": {
        "color": "#728E00"
    },
    "hljs-meta": {
        "color": "#434f54"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    },
    "hljs-function": {
        "color": "#728E00"
    },
    "hljs-number": {
        "color": "#8A7B52"
    }
}