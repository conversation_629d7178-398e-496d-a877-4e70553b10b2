{"logs": [{"outputFile": "com.visuallabstudio.ide.app-mergeDebugResources-53:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8894ffd5200146a245d974f5f73061d6\\transformed\\core-1.12.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "559,660,763,871,976,1080,1180,6460", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "655,758,866,971,1075,1175,1304,6556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3fdae449ec0975b3ed67992fb9c55c3c\\transformed\\jetified-ui-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,378,476,563,660,759,848,938,1006,1078,1161,1246,1325,1400,1466", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "195,280,373,471,558,655,754,843,933,1001,1073,1156,1241,1320,1395,1461,1579"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1309,1404,5089,5182,5353,5523,5620,5719,5808,5898,5966,6038,6121,6293,6666,6741,6807", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "1399,1484,5177,5275,5435,5615,5714,5803,5893,5961,6033,6116,6201,6367,6736,6802,6920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f063da582707269a21bd980d1f8ebd44\\transformed\\jetified-material3-1.1.2\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,395,509,592,682,790,930,1047,1185,1266,1364,1455,1550,1662,1786,1889,2016,2142,2267,2443,2559,2673,2794,2909,3001,3093,3210,3332,3425,3531,3635,3766,3906,4012,4109,4182,4265,4352,4440,4545,4623,4704,4799,4900,4994,5092,5175,5278,5371,5470,5599,5679,5778", "endColumns": "114,115,108,113,82,89,107,139,116,137,80,97,90,94,111,123,102,126,125,124,175,115,113,120,114,91,91,116,121,92,105,103,130,139,105,96,72,82,86,87,104,77,80,94,100,93,97,82,102,92,98,128,79,98,89", "endOffsets": "165,281,390,504,587,677,785,925,1042,1180,1261,1359,1450,1545,1657,1781,1884,2011,2137,2262,2438,2554,2668,2789,2904,2996,3088,3205,3327,3420,3526,3630,3761,3901,4007,4104,4177,4260,4347,4435,4540,4618,4699,4794,4895,4989,5087,5170,5273,5366,5465,5594,5674,5773,5863"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,336,445,1489,1572,1662,1770,1910,2027,2165,2246,2344,2435,2530,2642,2766,2869,2996,3122,3247,3423,3539,3653,3774,3889,3981,4073,4190,4312,4405,4511,4615,4746,4886,4992,5280,5440,6206,6372,6561,6925,7003,7084,7179,7280,7374,7472,7555,7658,7751,7850,7979,8059,8158", "endColumns": "114,115,108,113,82,89,107,139,116,137,80,97,90,94,111,123,102,126,125,124,175,115,113,120,114,91,91,116,121,92,105,103,130,139,105,96,72,82,86,87,104,77,80,94,100,93,97,82,102,92,98,128,79,98,89", "endOffsets": "215,331,440,554,1567,1657,1765,1905,2022,2160,2241,2339,2430,2525,2637,2761,2864,2991,3117,3242,3418,3534,3648,3769,3884,3976,4068,4185,4307,4400,4506,4610,4741,4881,4987,5084,5348,5518,6288,6455,6661,6998,7079,7174,7275,7369,7467,7550,7653,7746,7845,7974,8054,8153,8243"}}]}]}