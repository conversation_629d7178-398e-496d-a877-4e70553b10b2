{"logs": [{"outputFile": "com.visuallabstudio.ide.app-mergeDebugResources-53:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3fdae449ec0975b3ed67992fb9c55c3c\\transformed\\jetified-ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,997,1067,1151,1240,1312,1393,1464", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,992,1062,1146,1235,1307,1388,1459,1580"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1279,1383,5140,5236,5412,5574,5651,5741,5833,5917,5988,6058,6142,6319,6684,6765,6836", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "1378,1468,5231,5334,5492,5646,5736,5828,5912,5983,6053,6137,6226,6386,6760,6831,6952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8894ffd5200146a245d974f5f73061d6\\transformed\\core-1.12.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "560,658,760,857,955,1060,1163,6473", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "653,755,852,950,1055,1158,1274,6569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f063da582707269a21bd980d1f8ebd44\\transformed\\jetified-material3-1.1.2\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,284,396,510,586,677,786,915,1032,1176,1257,1352,1442,1534,1645,1767,1867,2008,2148,2286,2454,2581,2698,2822,2942,3033,3127,3249,3380,3476,3574,3683,3821,3968,4080,4177,4250,4327,4415,4497,4607,4691,4770,4867,4965,5058,5151,5235,5338,5434,5531,5660,5742,5849", "endColumns": "114,113,111,113,75,90,108,128,116,143,80,94,89,91,110,121,99,140,139,137,167,126,116,123,119,90,93,121,130,95,97,108,137,146,111,96,72,76,87,81,109,83,78,96,97,92,92,83,102,95,96,128,81,106,98", "endOffsets": "165,279,391,505,581,672,781,910,1027,1171,1252,1347,1437,1529,1640,1762,1862,2003,2143,2281,2449,2576,2693,2817,2937,3028,3122,3244,3375,3471,3569,3678,3816,3963,4075,4172,4245,4322,4410,4492,4602,4686,4765,4862,4960,5053,5146,5230,5333,5429,5526,5655,5737,5844,5943"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,334,446,1473,1549,1640,1749,1878,1995,2139,2220,2315,2405,2497,2608,2730,2830,2971,3111,3249,3417,3544,3661,3785,3905,3996,4090,4212,4343,4439,4537,4646,4784,4931,5043,5339,5497,6231,6391,6574,6957,7041,7120,7217,7315,7408,7501,7585,7688,7784,7881,8010,8092,8199", "endColumns": "114,113,111,113,75,90,108,128,116,143,80,94,89,91,110,121,99,140,139,137,167,126,116,123,119,90,93,121,130,95,97,108,137,146,111,96,72,76,87,81,109,83,78,96,97,92,92,83,102,95,96,128,81,106,98", "endOffsets": "215,329,441,555,1544,1635,1744,1873,1990,2134,2215,2310,2400,2492,2603,2725,2825,2966,3106,3244,3412,3539,3656,3780,3900,3991,4085,4207,4338,4434,4532,4641,4779,4926,5038,5135,5407,5569,6314,6468,6679,7036,7115,7212,7310,7403,7496,7580,7683,7779,7876,8005,8087,8194,8293"}}]}]}