#!/usr/bin/env pwsh

Write-Host "========================================" -ForegroundColor Green
Write-Host "Building Visual Lab Studio APK (Fixed)" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Set Android environment variables
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:ANDROID_SDK_ROOT = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH = "$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin;$env:PATH"

Write-Host "[1/5] Setting up environment..." -ForegroundColor Yellow
Write-Host "ANDROID_HOME: $env:ANDROID_HOME" -ForegroundColor Cyan
Write-Host "ANDROID_SDK_ROOT: $env:ANDROID_SDK_ROOT" -ForegroundColor Cyan

# Verify Android SDK exists
if (-not (Test-Path $env:ANDROID_HOME)) {
    Write-Host "ERROR: Android SDK not found at $env:ANDROID_HOME" -ForegroundColor Red
    Write-Host "Please install Android Studio and SDK first." -ForegroundColor Red
    exit 1
}

Write-Host "[2/5] Installing dependencies..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to install dependencies" -ForegroundColor Red
    exit 1
}

Write-Host "[3/5] Cleaning previous builds..." -ForegroundColor Yellow
Remove-Item -Path "android\app\build" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "android\build" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "[4/5] Testing Metro bundler..." -ForegroundColor Yellow
# Test if Metro can parse files
$metroTest = Start-Process -FilePath "npx" -ArgumentList "react-native", "bundle", "--platform", "android", "--dev", "false", "--entry-file", "index.js", "--bundle-output", "android/app/src/main/assets/index.android.bundle", "--assets-dest", "android/app/src/main/res" -Wait -PassThru -NoNewWindow

if ($metroTest.ExitCode -ne 0) {
    Write-Host "ERROR: Metro bundler failed. Checking for syntax errors..." -ForegroundColor Red
    npx tsc --noEmit
    exit 1
}

Write-Host "[5/5] Building APK..." -ForegroundColor Yellow
Set-Location android

# Build debug APK
.\gradlew assembleDebug --warning-mode all

if ($LASTEXITCODE -eq 0) {
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "BUILD SUCCESSFUL!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    
    $apkPath = "app\build\outputs\apk\debug\app-debug.apk"
    if (Test-Path $apkPath) {
        $fullPath = Resolve-Path $apkPath
        Write-Host "APK Location: $fullPath" -ForegroundColor Cyan
        
        # Copy APK to root directory for easy access
        Copy-Item $apkPath "..\VisualLabStudio-debug.apk" -Force
        Write-Host "APK copied to: VisualLabStudio-debug.apk" -ForegroundColor Cyan
        
        # Show APK info
        $apkSize = (Get-Item $apkPath).Length / 1MB
        Write-Host "APK Size: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Cyan
    } else {
        Write-Host "WARNING: APK file not found at expected location" -ForegroundColor Yellow
    }
} else {
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "BUILD FAILED!" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    exit 1
}

Set-Location ..
Write-Host "Build process completed!" -ForegroundColor Green
