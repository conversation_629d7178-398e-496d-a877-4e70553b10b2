import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '../contexts/ThemeContext';
import { FileEntity, Language } from '../types';

const EditorScreen: React.FC = () => {
  const { theme } = useTheme();
  const [currentFile, setCurrentFile] = useState<FileEntity | null>(null);
  const [content, setContent] = useState('');
  const [isModified, setIsModified] = useState(false);
  const [language, setLanguage] = useState<Language>('javascript');

  useEffect(() => {
    // Load default example file
    loadExampleFile();
  }, []);

  const loadExampleFile = () => {
    const exampleFile: FileEntity = {
      id: 'example-1',
      name: 'hello_world.js',
      path: '/examples/hello_world.js',
      content: `// Visual Lab Studio IDE - JavaScript Demo
console.log("=== Visual Lab Studio IDE - JavaScript Demo ===");

// Example 1: Variables and functions
function greet(name) {
    return \`Hello, \${name}! Welcome to Visual Lab Studio IDE.\`;
}

const message = greet("Developer");
console.log(message);

// Example 2: Array operations
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
console.log("Original:", numbers);
console.log("Doubled:", doubled);

// Example 3: Object manipulation
const project = {
    name: "Visual Lab Studio IDE",
    version: "1.0.0",
    language: "React Native",
    features: ["Code Editor", "Terminal", "File Explorer", "Settings"]
};

console.log("Project Info:", project);
console.log("Features:", project.features.join(", "));

console.log("IDE funcionando perfeitamente! 🚀");`,
      language: 'javascript',
      size: 0,
      lastModified: new Date(),
      isTemporary: true,
    };

    setCurrentFile(exampleFile);
    setContent(exampleFile.content);
    setLanguage(exampleFile.language as Language);
    setIsModified(false);
  };

  const handleContentChange = (text: string) => {
    setContent(text);
    setIsModified(true);
  };

  const handleSave = () => {
    if (currentFile) {
      // TODO: Implement file saving logic
      Alert.alert('Save', 'File saved successfully!');
      setIsModified(false);
    }
  };

  const handleNewFile = () => {
    if (isModified) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Do you want to save before creating a new file?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Don\'t Save', onPress: createNewFile },
          { text: 'Save', onPress: () => { handleSave(); createNewFile(); } },
        ]
      );
    } else {
      createNewFile();
    }
  };

  const createNewFile = () => {
    const newFile: FileEntity = {
      id: `new-${Date.now()}`,
      name: 'untitled.js',
      path: '',
      content: '',
      language: 'javascript',
      size: 0,
      lastModified: new Date(),
      isTemporary: true,
    };

    setCurrentFile(newFile);
    setContent('');
    setLanguage('javascript');
    setIsModified(false);
  };

  const handleRun = () => {
    // TODO: Implement code execution
    Alert.alert('Run', 'Code execution will be implemented soon!');
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header with file info and actions */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <View style={styles.fileInfo}>
          <Text style={[styles.fileName, { color: theme.colors.text }]}>
            {currentFile?.name || 'No file'}
            {isModified && <Text style={{ color: theme.colors.accent }}> •</Text>}
          </Text>
          <Text style={[styles.fileLanguage, { color: theme.colors.textSecondary }]}>
            {language.toUpperCase()}
          </Text>
        </View>
        
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleNewFile}
          >
            <Icon name="add" size={20} color={theme.colors.background} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.success }]}
            onPress={handleSave}
            disabled={!isModified}
          >
            <Icon name="save" size={20} color={theme.colors.background} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.accent }]}
            onPress={handleRun}
          >
            <Icon name="play-arrow" size={20} color={theme.colors.background} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Code Editor */}
      <ScrollView style={styles.editorContainer}>
        <TextInput
          style={[
            styles.editor,
            {
              color: theme.colors.text,
              backgroundColor: theme.colors.background,
              fontSize: theme.typography.fontSize.sm,
            }
          ]}
          value={content}
          onChangeText={handleContentChange}
          multiline
          placeholder="Start typing your code here..."
          placeholderTextColor={theme.colors.textSecondary}
          textAlignVertical="top"
          autoCapitalize="none"
          autoCorrect={false}
          spellCheck={false}
          keyboardType="ascii-capable"
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
  },
  fileLanguage: {
    fontSize: 12,
    marginTop: 2,
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editorContainer: {
    flex: 1,
  },
  editor: {
    flex: 1,
    padding: 16,
    fontFamily: 'monospace',
    lineHeight: 20,
    minHeight: '100%',
  },
});

export default EditorScreen;
