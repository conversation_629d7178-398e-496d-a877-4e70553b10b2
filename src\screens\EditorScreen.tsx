import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useFileSystem } from '../utils/useFileSystem';
import { Language } from '../types';

const EditorScreen: React.FC = () => {
  const { theme } = useTheme();
  const {
    currentFile,
    createFile,
    updateFileContent,
    saveFile,
    isLoading,
    error,
    clearError,
  } = useFileSystem();

  const [content, setContent] = useState('');
  const [isModified, setIsModified] = useState(false);
  const [language, setLanguage] = useState<Language>('javascript');

  useEffect(() => {
    if (currentFile) {
      setContent(currentFile.content);
      setLanguage(currentFile.language as Language);
      setIsModified(false);
    } else {
      // Load default example file
      loadExampleFile();
    }
  }, [currentFile]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
      clearError();
    }
  }, [error, clearError]);

  const loadExampleFile = async () => {
    const exampleContent = `// Visual Lab Studio IDE - JavaScript Demo
console.log("=== Visual Lab Studio IDE - JavaScript Demo ===");

// Example 1: Variables and functions
function greet(name) {
    return \`Hello, \${name}! Welcome to Visual Lab Studio IDE.\`;
}

const message = greet("Developer");
console.log(message);

// Example 2: Array operations
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
console.log("Original:", numbers);
console.log("Doubled:", doubled);

// Example 3: Object manipulation
const project = {
    name: "Visual Lab Studio IDE",
    version: "1.0.0",
    language: "React Native",
    features: ["Code Editor", "Terminal", "File Explorer", "Settings"]
};

console.log("Project Info:", project);
console.log("Features:", project.features.join(", "));

console.log("IDE funcionando perfeitamente! 🚀");`;

    const file = await createFile('hello_world.js', exampleContent, 'javascript');
    if (file) {
      setContent(file.content);
      setLanguage(file.language as Language);
      setIsModified(false);
    }
  };

  const handleContentChange = (text: string) => {
    setContent(text);
    setIsModified(true);
  };

  const handleSave = async () => {
    if (currentFile) {
      const updatedFile = await updateFileContent(currentFile.id, content);
      if (updatedFile) {
        setIsModified(false);
        Alert.alert('Success', 'File saved successfully!');
      }
    }
  };

  const handleSaveToDevice = async () => {
    if (currentFile) {
      const updatedFile = await updateFileContent(currentFile.id, content);
      if (updatedFile) {
        const filePath = await saveFile(updatedFile);
        if (filePath) {
          setIsModified(false);
          Alert.alert('Success', `File saved to device at: ${filePath}`);
        }
      }
    }
  };

  const handleNewFile = async () => {
    if (isModified) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Do you want to save before creating a new file?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: "Don't Save", onPress: createNewFile },
          { text: 'Save', onPress: () => { handleSave(); createNewFile(); } },
        ]
      );
    } else {
      createNewFile();
    }
  };

  const createNewFile = async () => {
    const file = await createFile('untitled.js', '', 'javascript');
    if (file) {
      setContent('');
      setLanguage('javascript');
      setIsModified(false);
    }
  };

  const handleRun = () => {
    // TODO: Implement code execution
    Alert.alert('Run', 'Code execution will be implemented soon!');
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header with file info and actions */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <View style={styles.fileInfo}>
          <Text style={[styles.fileName, { color: theme.colors.text }]}>
            {currentFile?.name || 'No file'}
            {isModified && <Text style={{ color: theme.colors.accent }}> •</Text>}
          </Text>
          <Text style={[styles.fileLanguage, { color: theme.colors.textSecondary }]}>
            {language.toUpperCase()}
          </Text>
        </View>
        
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleNewFile}
            disabled={isLoading}
          >
            <Text style={styles.actionButtonText}>New</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.success }]}
            onPress={handleSave}
            disabled={!isModified || isLoading}
          >
            <Text style={styles.actionButtonText}>Save</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.warning }]}
            onPress={handleSaveToDevice}
            disabled={!currentFile || isLoading}
          >
            <Text style={styles.actionButtonText}>Export</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.accent }]}
            onPress={handleRun}
            disabled={isLoading}
          >
            <Text style={styles.actionButtonText}>Run</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Code Editor */}
      <ScrollView style={styles.editorContainer}>
        <TextInput
          style={[
            styles.editor,
            {
              color: theme.colors.text,
              backgroundColor: theme.colors.background,
              fontSize: theme.typography.fontSize.sm,
            }
          ]}
          value={content}
          onChangeText={handleContentChange}
          multiline
          placeholder="Start typing your code here..."
          placeholderTextColor={theme.colors.textSecondary}
          textAlignVertical="top"
          autoCapitalize="none"
          autoCorrect={false}
          spellCheck={false}
          keyboardType="ascii-capable"
          editable={!isLoading}
        />
      </ScrollView>

      {/* Status Bar */}
      {isLoading && (
        <View style={[styles.statusBar, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.statusText, { color: theme.colors.textSecondary }]}>
            Loading...
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
  },
  fileLanguage: {
    fontSize: 12,
    marginTop: 2,
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  editorContainer: {
    flex: 1,
  },
  editor: {
    flex: 1,
    padding: 16,
    fontFamily: 'monospace',
    lineHeight: 20,
    minHeight: '100%',
  },
  statusBar: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
  },
  statusText: {
    fontSize: 12,
  },
});

export default EditorScreen;
