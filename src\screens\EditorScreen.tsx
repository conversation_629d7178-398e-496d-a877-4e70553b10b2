import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useFileSystem } from '../utils/useFileSystem';
import { CodeExecutionService } from '../services/CodeExecutionService';
import CodeEditor from '../components/CodeEditor';
import { Language } from '../types';

const EditorScreen: React.FC = () => {
  const { theme } = useTheme();
  const {
    currentFile,
    createFile,
    updateFileContent,
    saveFile,
    isLoading,
    error,
    clearError,
  } = useFileSystem();

  const [content, setContent] = useState('');
  const [isModified, setIsModified] = useState(false);
  const [language, setLanguage] = useState<Language>('javascript');
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<string>('');

  useEffect(() => {
    if (currentFile) {
      setContent(currentFile.content);
      setLanguage(currentFile.language as Language);
      setIsModified(false);
    } else {
      // Load default example file
      loadExampleFile();
    }
  }, [currentFile]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error);
      clearError();
    }
  }, [error, clearError]);

  const loadExampleFile = async () => {
    const exampleContent = `// Visual Lab Studio IDE - JavaScript Demo
console.log("=== Visual Lab Studio IDE - JavaScript Demo ===");

// Example 1: Variables and functions
function greet(name) {
    return \`Hello, \${name}! Welcome to Visual Lab Studio IDE.\`;
}

const message = greet("Developer");
console.log(message);

// Example 2: Array operations
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
console.log("Original:", numbers);
console.log("Doubled:", doubled);

// Example 3: Object manipulation
const project = {
    name: "Visual Lab Studio IDE",
    version: "1.0.0",
    language: "React Native",
    features: ["Code Editor", "Terminal", "File Explorer", "Settings"]
};

console.log("Project Info:", project);
console.log("Features:", project.features.join(", "));

console.log("IDE funcionando perfeitamente! 🚀");`;

    const file = await createFile('hello_world.js', exampleContent, 'javascript');
    if (file) {
      setContent(file.content);
      setLanguage(file.language as Language);
      setIsModified(false);
    }
  };

  const handleContentChange = (text: string) => {
    setContent(text);
    setIsModified(true);
  };

  const handleSave = async () => {
    if (currentFile) {
      const updatedFile = await updateFileContent(currentFile.id, content);
      if (updatedFile) {
        setIsModified(false);
        Alert.alert('Success', 'File saved successfully!');
      }
    }
  };

  const handleSaveToDevice = async () => {
    if (currentFile) {
      const updatedFile = await updateFileContent(currentFile.id, content);
      if (updatedFile) {
        const filePath = await saveFile(updatedFile);
        if (filePath) {
          setIsModified(false);
          Alert.alert('Success', `File saved to device at: ${filePath}`);
        }
      }
    }
  };

  const handleNewFile = async () => {
    if (isModified) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Do you want to save before creating a new file?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: "Don't Save", onPress: createNewFile },
          { text: 'Save', onPress: () => { handleSave(); createNewFile(); } },
        ]
      );
    } else {
      createNewFile();
    }
  };

  const createNewFile = async () => {
    const file = await createFile('untitled.js', '', 'javascript');
    if (file) {
      setContent('');
      setLanguage('javascript');
      setIsModified(false);
    }
  };

  const handleRun = async () => {
    if (!content.trim()) {
      Alert.alert('Error', 'No code to execute');
      return;
    }

    if (!CodeExecutionService.isLanguageSupported(language)) {
      Alert.alert('Error', `Code execution not supported for ${language}`);
      return;
    }

    // Validate code before execution
    const validation = CodeExecutionService.validateCode(content, language);
    if (!validation.isValid) {
      Alert.alert('Code Validation Error', validation.error || 'Invalid code');
      return;
    }

    setIsExecuting(true);
    setExecutionResult('');

    try {
      const result = await CodeExecutionService.executeCodeWithTimeout(content, language, 10000);

      let resultText = '';
      if (result.output) {
        resultText += `Output:\n${result.output}\n`;
      }
      if (result.error) {
        resultText += `Error:\n${result.error}\n`;
      }
      resultText += `\nExecution time: ${result.executionTime}ms`;

      setExecutionResult(resultText);

      // Show result in alert as well
      if (result.error) {
        Alert.alert('Execution Error', result.error);
      } else {
        Alert.alert('Execution Complete', result.output || 'Code executed successfully');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown execution error';
      setExecutionResult(`Execution failed: ${errorMessage}`);
      Alert.alert('Execution Failed', errorMessage);
    } finally {
      setIsExecuting(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header with file info and actions */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <View style={styles.fileInfo}>
          <Text style={[styles.fileName, { color: theme.colors.text }]}>
            {currentFile?.name || 'No file'}
            {isModified && <Text style={{ color: theme.colors.accent }}> •</Text>}
          </Text>
          <Text style={[styles.fileLanguage, { color: theme.colors.textSecondary }]}>
            {language.toUpperCase()}
          </Text>
        </View>
        
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleNewFile}
            disabled={isLoading}
          >
            <Text style={styles.actionButtonText}>New</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.success }]}
            onPress={handleSave}
            disabled={!isModified || isLoading}
          >
            <Text style={styles.actionButtonText}>Save</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.warning }]}
            onPress={handleSaveToDevice}
            disabled={!currentFile || isLoading}
          >
            <Text style={styles.actionButtonText}>Export</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.actionButton,
              {
                backgroundColor: isExecuting ? theme.colors.textSecondary : theme.colors.accent
              }
            ]}
            onPress={handleRun}
            disabled={isLoading || isExecuting}
          >
            <Text style={styles.actionButtonText}>
              {isExecuting ? 'Running...' : 'Run'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Code Editor */}
      <View style={styles.editorContainer}>
        <CodeEditor
          value={content}
          onChangeText={handleContentChange}
          language={language}
          placeholder="Start typing your code here..."
          editable={!isLoading && !isExecuting}
          showLineNumbers={true}
        />
      </View>

      {/* Execution Result */}
      {executionResult && (
        <View style={[styles.resultContainer, { backgroundColor: theme.colors.surface, borderTopColor: theme.colors.border }]}>
          <Text style={[styles.resultTitle, { color: theme.colors.text }]}>Execution Result:</Text>
          <ScrollView style={styles.resultScroll} nestedScrollEnabled>
            <Text style={[styles.resultText, { color: theme.colors.text }]}>
              {executionResult}
            </Text>
          </ScrollView>
          <TouchableOpacity
            style={[styles.clearResultButton, { backgroundColor: theme.colors.error }]}
            onPress={() => setExecutionResult('')}
          >
            <Text style={[styles.clearResultButtonText, { color: theme.colors.background }]}>
              Clear
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Status Bar */}
      {(isLoading || isExecuting) && (
        <View style={[styles.statusBar, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.statusText, { color: theme.colors.textSecondary }]}>
            {isExecuting ? 'Executing code...' : 'Loading...'}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
  },
  fileLanguage: {
    fontSize: 12,
    marginTop: 2,
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  editorContainer: {
    flex: 1,
  },
  resultContainer: {
    maxHeight: 200,
    borderTopWidth: 1,
    padding: 16,
  },
  resultTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  resultScroll: {
    flex: 1,
    maxHeight: 120,
  },
  resultText: {
    fontFamily: 'monospace',
    fontSize: 12,
    lineHeight: 16,
  },
  clearResultButton: {
    alignSelf: 'flex-end',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginTop: 8,
  },
  clearResultButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  statusBar: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
  },
  statusText: {
    fontSize: 12,
  },
});

export default EditorScreen;
