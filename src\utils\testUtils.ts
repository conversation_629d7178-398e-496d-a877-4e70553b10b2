import { FileService } from '../services/FileService';
import { CodeExecutionService } from '../services/CodeExecutionService';
import { DatabaseService } from '../services/DatabaseService';
import { FileEntity, Language } from '../types';

export class TestUtils {
  // Test file operations
  static async testFileOperations(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Testing file operations...');
      
      // Test file creation
      const testFile = await FileService.createFile('test.js', 'console.log("Hello Test!");', 'javascript');
      if (!testFile) {
        return { success: false, message: 'Failed to create test file' };
      }
      
      // Test file retrieval
      const retrievedFile = await FileService.getFileById(testFile.id);
      if (!retrievedFile || retrievedFile.content !== testFile.content) {
        return { success: false, message: 'Failed to retrieve test file' };
      }
      
      // Test file update
      const updatedFile = await FileService.updateFileContent(testFile.id, 'console.log("Updated test!");');
      if (!updatedFile || updatedFile.content !== 'console.log("Updated test!");') {
        return { success: false, message: 'Failed to update test file' };
      }
      
      // Test file deletion
      const deleted = await FileService.deleteFile(testFile.id);
      if (!deleted) {
        return { success: false, message: 'Failed to delete test file' };
      }
      
      console.log('File operations test passed!');
      return { success: true, message: 'All file operations working correctly' };
      
    } catch (error) {
      return { 
        success: false, 
        message: `File operations test failed: ${error instanceof Error ? error.message : error}` 
      };
    }
  }

  // Test code execution
  static async testCodeExecution(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Testing code execution...');
      
      // Test JavaScript execution
      const jsResult = await CodeExecutionService.executeCode('console.log("Hello JS!");', 'javascript');
      if (jsResult.error || !jsResult.output.includes('Hello JS!')) {
        return { success: false, message: 'JavaScript execution failed' };
      }
      
      // Test Python execution (simulated)
      const pyResult = await CodeExecutionService.executeCode('print("Hello Python!")', 'python');
      if (pyResult.error) {
        return { success: false, message: 'Python execution failed' };
      }
      
      // Test error handling
      const errorResult = await CodeExecutionService.executeCode('invalid syntax here', 'javascript');
      if (!errorResult.error) {
        return { success: false, message: 'Error handling not working' };
      }
      
      console.log('Code execution test passed!');
      return { success: true, message: 'Code execution working correctly' };
      
    } catch (error) {
      return { 
        success: false, 
        message: `Code execution test failed: ${error instanceof Error ? error.message : error}` 
      };
    }
  }

  // Test database operations
  static async testDatabaseOperations(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Testing database operations...');
      
      // Test file storage in database
      const testFile: FileEntity = {
        id: 'test-db-file',
        name: 'test-db.js',
        path: '',
        content: 'console.log("Database test");',
        language: 'javascript',
        size: 30,
        lastModified: new Date(),
        isTemporary: true,
      };
      
      await DatabaseService.saveFile(testFile);
      
      // Test file retrieval from database
      const retrievedFile = await DatabaseService.getFileById(testFile.id);
      if (!retrievedFile || retrievedFile.content !== testFile.content) {
        return { success: false, message: 'Database file operations failed' };
      }
      
      // Test file deletion from database
      await DatabaseService.deleteFile(testFile.id);
      const deletedFile = await DatabaseService.getFileById(testFile.id);
      if (deletedFile) {
        return { success: false, message: 'Database file deletion failed' };
      }
      
      console.log('Database operations test passed!');
      return { success: true, message: 'Database operations working correctly' };
      
    } catch (error) {
      return { 
        success: false, 
        message: `Database operations test failed: ${error instanceof Error ? error.message : error}` 
      };
    }
  }

  // Run all tests
  static async runAllTests(): Promise<{ success: boolean; results: string[] }> {
    const results: string[] = [];
    let allPassed = true;
    
    console.log('Running all tests...');
    
    // Test file operations
    const fileTest = await this.testFileOperations();
    results.push(`File Operations: ${fileTest.success ? '✅ PASS' : '❌ FAIL'} - ${fileTest.message}`);
    if (!fileTest.success) allPassed = false;
    
    // Test code execution
    const codeTest = await this.testCodeExecution();
    results.push(`Code Execution: ${codeTest.success ? '✅ PASS' : '❌ FAIL'} - ${codeTest.message}`);
    if (!codeTest.success) allPassed = false;
    
    // Test database operations
    const dbTest = await this.testDatabaseOperations();
    results.push(`Database Operations: ${dbTest.success ? '✅ PASS' : '❌ FAIL'} - ${dbTest.message}`);
    if (!dbTest.success) allPassed = false;
    
    console.log('All tests completed!');
    return { success: allPassed, results };
  }

  // Performance test
  static async performanceTest(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Running performance tests...');
      
      const startTime = Date.now();
      
      // Test file creation performance
      const files: FileEntity[] = [];
      for (let i = 0; i < 10; i++) {
        const file = await FileService.createFile(`perf-test-${i}.js`, `console.log("Performance test ${i}");`, 'javascript');
        if (file) files.push(file);
      }
      
      // Test file retrieval performance
      for (const file of files) {
        await FileService.getFileById(file.id);
      }
      
      // Test code execution performance
      for (let i = 0; i < 5; i++) {
        await CodeExecutionService.executeCode('console.log("Performance test");', 'javascript');
      }
      
      // Cleanup
      for (const file of files) {
        await FileService.deleteFile(file.id);
      }
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      console.log(`Performance test completed in ${totalTime}ms`);
      
      if (totalTime > 10000) { // 10 seconds
        return { success: false, message: `Performance test too slow: ${totalTime}ms` };
      }
      
      return { success: true, message: `Performance test passed: ${totalTime}ms` };
      
    } catch (error) {
      return { 
        success: false, 
        message: `Performance test failed: ${error instanceof Error ? error.message : error}` 
      };
    }
  }

  // Memory usage test
  static getMemoryUsage(): string {
    if (typeof global !== 'undefined' && global.gc) {
      global.gc();
    }
    
    const memoryUsage = process.memoryUsage ? process.memoryUsage() : null;
    if (memoryUsage) {
      return `Memory Usage: RSS: ${Math.round(memoryUsage.rss / 1024 / 1024)}MB, Heap: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`;
    }
    
    return 'Memory usage information not available';
  }

  // Create sample files for testing
  static async createSampleFiles(): Promise<FileEntity[]> {
    const sampleFiles = [
      {
        name: 'hello.js',
        content: `// JavaScript Hello World
console.log("Hello, World!");
console.log("Welcome to Visual Lab Studio IDE!");

function greet(name) {
    return \`Hello, \${name}!\`;
}

console.log(greet("Developer"));`,
        language: 'javascript' as Language,
      },
      {
        name: 'calculator.py',
        content: `# Python Calculator
def add(a, b):
    return a + b

def subtract(a, b):
    return a - b

def multiply(a, b):
    return a * b

def divide(a, b):
    if b != 0:
        return a / b
    else:
        return "Cannot divide by zero"

# Test the calculator
print("Calculator Demo")
print("5 + 3 =", add(5, 3))
print("10 - 4 =", subtract(10, 4))
print("6 * 7 =", multiply(6, 7))
print("15 / 3 =", divide(15, 3))`,
        language: 'python' as Language,
      },
      {
        name: 'data.json',
        content: `{
  "name": "Visual Lab Studio IDE",
  "version": "1.0.0",
  "description": "A powerful mobile IDE built with React Native",
  "features": [
    "Code Editor with Syntax Highlighting",
    "Terminal for Command Execution",
    "File Explorer",
    "Code Execution (JavaScript & Python)",
    "Dark/Light Theme",
    "Offline-first Architecture"
  ],
  "supported_languages": ["javascript", "python", "html", "css", "json", "markdown"],
  "author": "Visual Lab Studio Team"
}`,
        language: 'json' as Language,
      }
    ];

    const createdFiles: FileEntity[] = [];
    
    for (const sample of sampleFiles) {
      const file = await FileService.createFile(sample.name, sample.content, sample.language);
      if (file) {
        createdFiles.push(file);
      }
    }
    
    return createdFiles;
  }
}
