import { ExecutionResult, Language } from '../types';

export class CodeExecutionService {
  // Execute JavaScript code
  static async executeJavaScript(code: string): Promise<ExecutionResult> {
    const startTime = Date.now();
    let output = '';
    let error: string | undefined;

    try {
      // Create a custom console that captures output
      const capturedLogs: string[] = [];
      const customConsole = {
        log: (...args: any[]) => {
          capturedLogs.push(args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
          ).join(' '));
        },
        error: (...args: any[]) => {
          capturedLogs.push('ERROR: ' + args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
          ).join(' '));
        },
        warn: (...args: any[]) => {
          capturedLogs.push('WARN: ' + args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
          ).join(' '));
        },
        info: (...args: any[]) => {
          capturedLogs.push('INFO: ' + args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
          ).join(' '));
        },
      };

      // Create a safe execution environment
      const safeGlobals = {
        console: customConsole,
        Math,
        Date,
        JSON,
        Array,
        Object,
        String,
        Number,
        Boolean,
        RegExp,
        parseInt,
        parseFloat,
        isNaN,
        isFinite,
        setTimeout: (fn: () => void, delay: number) => {
          if (delay > 5000) throw new Error('Timeout too long (max 5 seconds)');
          return setTimeout(fn, delay);
        },
        clearTimeout,
        setInterval: (fn: () => void, delay: number) => {
          if (delay < 100) throw new Error('Interval too short (min 100ms)');
          return setInterval(fn, delay);
        },
        clearInterval,
      };

      // Wrap code in a function to limit scope
      const wrappedCode = `
        (function() {
          'use strict';
          ${code}
        })();
      `;

      // Execute the code with limited globals
      const func = new Function(...Object.keys(safeGlobals), wrappedCode);
      const result = func(...Object.values(safeGlobals));

      // If the code returns a value, add it to output
      if (result !== undefined) {
        capturedLogs.push('Return value: ' + (
          typeof result === 'object' ? JSON.stringify(result, null, 2) : String(result)
        ));
      }

      output = capturedLogs.join('\n');
      
      if (!output) {
        output = 'Code executed successfully (no output)';
      }

    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      output = '';
    }

    return {
      output,
      error,
      executionTime: Date.now() - startTime,
    };
  }

  // Execute Python code (simulated - would need a Python interpreter)
  static async executePython(code: string): Promise<ExecutionResult> {
    const startTime = Date.now();
    
    // For now, we'll simulate Python execution with basic pattern matching
    // In a real implementation, you'd use a Python interpreter like Pyodide or Chaquopy
    
    try {
      const lines = code.split('\n').filter(line => line.trim());
      const output: string[] = [];
      
      for (const line of lines) {
        const trimmedLine = line.trim();
        
        // Handle print statements
        const printMatch = trimmedLine.match(/^print\s*\(\s*(.+)\s*\)$/);
        if (printMatch) {
          let content = printMatch[1];
          
          // Remove quotes from strings
          if ((content.startsWith('"') && content.endsWith('"')) || 
              (content.startsWith("'") && content.endsWith("'"))) {
            content = content.slice(1, -1);
          }
          
          output.push(content);
          continue;
        }
        
        // Handle simple variable assignments
        const assignMatch = trimmedLine.match(/^(\w+)\s*=\s*(.+)$/);
        if (assignMatch) {
          output.push(`Variable '${assignMatch[1]}' assigned`);
          continue;
        }
        
        // Handle simple math expressions
        const mathMatch = trimmedLine.match(/^print\s*\(\s*(\d+\s*[+\-*/]\s*\d+)\s*\)$/);
        if (mathMatch) {
          try {
            const result = eval(mathMatch[1]);
            output.push(String(result));
          } catch {
            output.push('Math expression error');
          }
          continue;
        }
        
        // Handle comments
        if (trimmedLine.startsWith('#')) {
          continue;
        }
        
        // Default for unrecognized lines
        if (trimmedLine) {
          output.push(`Executed: ${trimmedLine}`);
        }
      }
      
      return {
        output: output.length > 0 ? output.join('\n') : 'Python code executed (simulated)',
        executionTime: Date.now() - startTime,
      };
      
    } catch (err) {
      return {
        output: '',
        error: `Python execution error: ${err instanceof Error ? err.message : String(err)}`,
        executionTime: Date.now() - startTime,
      };
    }
  }

  // Execute code based on language
  static async executeCode(code: string, language: Language): Promise<ExecutionResult> {
    if (!code.trim()) {
      return {
        output: '',
        error: 'No code to execute',
        executionTime: 0,
      };
    }

    switch (language.toLowerCase()) {
      case 'javascript':
      case 'js':
        return this.executeJavaScript(code);
      
      case 'python':
      case 'py':
        return this.executePython(code);
      
      default:
        return {
          output: '',
          error: `Code execution not supported for language: ${language}`,
          executionTime: 0,
        };
    }
  }

  // Get supported languages
  static getSupportedLanguages(): Language[] {
    return ['javascript', 'python'];
  }

  // Check if language is supported
  static isLanguageSupported(language: Language): boolean {
    return this.getSupportedLanguages().includes(language);
  }

  // Execute code with timeout
  static async executeCodeWithTimeout(
    code: string, 
    language: Language, 
    timeoutMs: number = 10000
  ): Promise<ExecutionResult> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        resolve({
          output: '',
          error: `Execution timed out after ${timeoutMs}ms`,
          executionTime: timeoutMs,
        });
      }, timeoutMs);

      this.executeCode(code, language)
        .then((result) => {
          clearTimeout(timeout);
          resolve(result);
        })
        .catch((error) => {
          clearTimeout(timeout);
          resolve({
            output: '',
            error: error instanceof Error ? error.message : String(error),
            executionTime: timeoutMs,
          });
        });
    });
  }

  // Validate code before execution (basic security checks)
  static validateCode(code: string, language: Language): { isValid: boolean; error?: string } {
    if (!code.trim()) {
      return { isValid: false, error: 'Code cannot be empty' };
    }

    if (code.length > 10000) {
      return { isValid: false, error: 'Code too long (max 10,000 characters)' };
    }

    // Basic security checks for JavaScript
    if (language === 'javascript') {
      const dangerousPatterns = [
        /require\s*\(/,
        /import\s+/,
        /eval\s*\(/,
        /Function\s*\(/,
        /XMLHttpRequest/,
        /fetch\s*\(/,
        /localStorage/,
        /sessionStorage/,
        /document\./,
        /window\./,
        /global\./,
        /process\./,
        /fs\./,
        /child_process/,
      ];

      for (const pattern of dangerousPatterns) {
        if (pattern.test(code)) {
          return { 
            isValid: false, 
            error: `Potentially unsafe code detected: ${pattern.source}` 
          };
        }
      }
    }

    return { isValid: true };
  }
}
