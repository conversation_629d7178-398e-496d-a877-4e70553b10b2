import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

interface SettingItemProps {
  title: string;
  subtitle?: string;
  onPress?: () => void;
  rightComponent?: React.ReactNode;
}

const SettingItem: React.FC<SettingItemProps> = ({
  title,
  subtitle,
  onPress,
  rightComponent,
}) => {
  const { theme } = useTheme();

  return (
    <TouchableOpacity
      style={[
        styles.settingItem,
        {
          backgroundColor: theme.colors.surface,
          borderBottomColor: theme.colors.border,
        }
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingContent}>
        <Text style={[styles.settingTitle, { color: theme.colors.text }]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary }]}>
            {subtitle}
          </Text>
        )}
      </View>
      
      {rightComponent || (
        onPress && <Text style={[styles.chevron, { color: theme.colors.textSecondary }]}>›</Text>
      )}
    </TouchableOpacity>
  );
};

interface SettingSectionProps {
  title: string;
  children: React.ReactNode;
}

const SettingSection: React.FC<SettingSectionProps> = ({ title, children }) => {
  const { theme } = useTheme();

  return (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.textSecondary }]}>
        {title.toUpperCase()}
      </Text>
      <View style={[styles.sectionContent, { backgroundColor: theme.colors.surface }]}>
        {children}
      </View>
    </View>
  );
};

const SettingsScreen: React.FC = () => {
  const { theme, isDark, toggleTheme } = useTheme();
  const [autoSave, setAutoSave] = useState(true);
  const [wordWrap, setWordWrap] = useState(true);
  const [showLineNumbers, setShowLineNumbers] = useState(true);

  const handleAbout = () => {
    Alert.alert(
      'About Visual Lab Studio IDE',
      'Version 1.0.0\n\nA powerful mobile IDE built with React Native.\n\nFeatures:\n• Code Editor with syntax highlighting\n• Terminal for command execution\n• File Explorer\n• Offline-first architecture\n\nBuilt with ❤️ for developers',
      [{ text: 'OK' }]
    );
  };

  const handleExportSettings = () => {
    Alert.alert(
      'Export Settings',
      'Settings export will be implemented soon!',
      [{ text: 'OK' }]
    );
  };

  const handleImportSettings = () => {
    Alert.alert(
      'Import Settings',
      'Settings import will be implemented soon!',
      [{ text: 'OK' }]
    );
  };

  const handleResetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to default?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            setAutoSave(true);
            setWordWrap(true);
            setShowLineNumbers(true);
            Alert.alert('Success', 'Settings reset to default');
          },
        },
      ]
    );
  };

  const handleFontSizeChange = () => {
    Alert.alert(
      'Font Size',
      'Font size adjustment will be implemented soon!',
      [{ text: 'OK' }]
    );
  };

  const handleTabSizeChange = () => {
    Alert.alert(
      'Tab Size',
      'Tab size adjustment will be implemented soon!',
      [{ text: 'OK' }]
    );
  };

  const handleDefaultLanguageChange = () => {
    Alert.alert(
      'Default Language',
      'Language selection will be implemented soon!',
      [{ text: 'OK' }]
    );
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.content}
    >
      {/* Appearance */}
      <SettingSection title="Appearance">
        <SettingItem
          title="Dark Theme"
          subtitle={isDark ? 'Enabled' : 'Disabled'}
          rightComponent={
            <Switch
              value={isDark}
              onValueChange={toggleTheme}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.background}
            />
          }
        />
        <SettingItem
          title="Font Size"
          subtitle="14px"
          onPress={handleFontSizeChange}
        />
      </SettingSection>

      {/* Editor */}
      <SettingSection title="Editor">
        <SettingItem
          title="Auto Save"
          subtitle="Automatically save changes"
          rightComponent={
            <Switch
              value={autoSave}
              onValueChange={setAutoSave}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.background}
            />
          }
        />
        <SettingItem
          title="Word Wrap"
          subtitle="Wrap long lines"
          rightComponent={
            <Switch
              value={wordWrap}
              onValueChange={setWordWrap}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.background}
            />
          }
        />
        <SettingItem
          title="Line Numbers"
          subtitle="Show line numbers"
          rightComponent={
            <Switch
              value={showLineNumbers}
              onValueChange={setShowLineNumbers}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.background}
            />
          }
        />
        <SettingItem
          title="Tab Size"
          subtitle="2 spaces"
          onPress={handleTabSizeChange}
        />
        <SettingItem
          title="Default Language"
          subtitle="JAVASCRIPT"
          onPress={handleDefaultLanguageChange}
        />
      </SettingSection>

      {/* Data */}
      <SettingSection title="Data">
        <SettingItem
          title="Export Settings"
          subtitle="Save settings to file"
          onPress={handleExportSettings}
        />
        <SettingItem
          title="Import Settings"
          subtitle="Load settings from file"
          onPress={handleImportSettings}
        />
        <SettingItem
          title="Reset Settings"
          subtitle="Restore default settings"
          onPress={handleResetSettings}
        />
      </SettingSection>

      {/* About */}
      <SettingSection title="About">
        <SettingItem
          title="About"
          subtitle="Version 1.0.0"
          onPress={handleAbout}
        />
      </SettingSection>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    paddingBottom: 32,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginHorizontal: 16,
    marginBottom: 8,
  },
  sectionContent: {
    marginHorizontal: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  chevron: {
    fontSize: 20,
  },
});

export default SettingsScreen;
