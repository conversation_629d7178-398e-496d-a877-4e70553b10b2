import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '../contexts/ThemeContext';
import { UserSettingsEntity } from '../types';

interface SettingItemProps {
  title: string;
  subtitle?: string;
  icon: string;
  onPress?: () => void;
  rightComponent?: React.ReactNode;
}

const SettingItem: React.FC<SettingItemProps> = ({
  title,
  subtitle,
  icon,
  onPress,
  rightComponent,
}) => {
  const { theme } = useTheme();

  return (
    <TouchableOpacity
      style={[
        styles.settingItem,
        {
          backgroundColor: theme.colors.surface,
          borderBottomColor: theme.colors.border,
        }
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingIcon}>
        <Icon name={icon} size={24} color={theme.colors.primary} />
      </View>
      
      <View style={styles.settingContent}>
        <Text style={[styles.settingTitle, { color: theme.colors.text }]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary }]}>
            {subtitle}
          </Text>
        )}
      </View>
      
      {rightComponent || (
        onPress && <Icon name="chevron-right" size={20} color={theme.colors.textSecondary} />
      )}
    </TouchableOpacity>
  );
};

interface SettingSectionProps {
  title: string;
  children: React.ReactNode;
}

const SettingSection: React.FC<SettingSectionProps> = ({ title, children }) => {
  const { theme } = useTheme();

  return (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.textSecondary }]}>
        {title.toUpperCase()}
      </Text>
      <View style={[styles.sectionContent, { backgroundColor: theme.colors.surface }]}>
        {children}
      </View>
    </View>
  );
};

const SettingsScreen: React.FC = () => {
  const { theme, isDark, toggleTheme } = useTheme();
  const [settings, setSettings] = useState<UserSettingsEntity>({
    id: '1',
    theme: isDark ? 'dark' : 'light',
    fontSize: 14,
    fontFamily: 'monospace',
    autoSave: true,
    wordWrap: true,
    showLineNumbers: true,
    tabSize: 2,
    defaultLanguage: 'javascript',
  });

  useEffect(() => {
    // Load settings from storage
    loadSettings();
  }, []);

  const loadSettings = async () => {
    // TODO: Load from AsyncStorage or database
    console.log('Loading settings...');
  };

  const saveSettings = async (newSettings: UserSettingsEntity) => {
    setSettings(newSettings);
    // TODO: Save to AsyncStorage or database
    console.log('Saving settings:', newSettings);
  };

  const handleThemeToggle = () => {
    toggleTheme();
    saveSettings({
      ...settings,
      theme: isDark ? 'light' : 'dark',
    });
  };

  const handleAutoSaveToggle = (value: boolean) => {
    saveSettings({
      ...settings,
      autoSave: value,
    });
  };

  const handleWordWrapToggle = (value: boolean) => {
    saveSettings({
      ...settings,
      wordWrap: value,
    });
  };

  const handleLineNumbersToggle = (value: boolean) => {
    saveSettings({
      ...settings,
      showLineNumbers: value,
    });
  };

  const handleFontSizeChange = () => {
    Alert.alert(
      'Font Size',
      'Font size adjustment will be implemented soon!',
      [{ text: 'OK' }]
    );
  };

  const handleTabSizeChange = () => {
    Alert.alert(
      'Tab Size',
      'Tab size adjustment will be implemented soon!',
      [{ text: 'OK' }]
    );
  };

  const handleDefaultLanguageChange = () => {
    Alert.alert(
      'Default Language',
      'Language selection will be implemented soon!',
      [{ text: 'OK' }]
    );
  };

  const handleAbout = () => {
    Alert.alert(
      'About Visual Lab Studio IDE',
      'Version 1.0.0\n\nA powerful mobile IDE built with React Native.\n\nFeatures:\n• Code Editor with syntax highlighting\n• Terminal for command execution\n• File Explorer\n• Offline-first architecture\n\nBuilt with ❤️ for developers',
      [{ text: 'OK' }]
    );
  };

  const handleExportSettings = () => {
    Alert.alert(
      'Export Settings',
      'Settings export will be implemented soon!',
      [{ text: 'OK' }]
    );
  };

  const handleImportSettings = () => {
    Alert.alert(
      'Import Settings',
      'Settings import will be implemented soon!',
      [{ text: 'OK' }]
    );
  };

  const handleResetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to default?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            const defaultSettings: UserSettingsEntity = {
              id: '1',
              theme: 'dark',
              fontSize: 14,
              fontFamily: 'monospace',
              autoSave: true,
              wordWrap: true,
              showLineNumbers: true,
              tabSize: 2,
              defaultLanguage: 'javascript',
            };
            saveSettings(defaultSettings);
            Alert.alert('Success', 'Settings reset to default');
          },
        },
      ]
    );
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.content}
    >
      {/* Appearance */}
      <SettingSection title="Appearance">
        <SettingItem
          title="Dark Theme"
          subtitle={isDark ? 'Enabled' : 'Disabled'}
          icon="palette"
          rightComponent={
            <Switch
              value={isDark}
              onValueChange={handleThemeToggle}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.background}
            />
          }
        />
        <SettingItem
          title="Font Size"
          subtitle={`${settings.fontSize}px`}
          icon="format-size"
          onPress={handleFontSizeChange}
        />
      </SettingSection>

      {/* Editor */}
      <SettingSection title="Editor">
        <SettingItem
          title="Auto Save"
          subtitle="Automatically save changes"
          icon="save"
          rightComponent={
            <Switch
              value={settings.autoSave}
              onValueChange={handleAutoSaveToggle}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.background}
            />
          }
        />
        <SettingItem
          title="Word Wrap"
          subtitle="Wrap long lines"
          icon="wrap-text"
          rightComponent={
            <Switch
              value={settings.wordWrap}
              onValueChange={handleWordWrapToggle}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.background}
            />
          }
        />
        <SettingItem
          title="Line Numbers"
          subtitle="Show line numbers"
          icon="format-list-numbered"
          rightComponent={
            <Switch
              value={settings.showLineNumbers}
              onValueChange={handleLineNumbersToggle}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.background}
            />
          }
        />
        <SettingItem
          title="Tab Size"
          subtitle={`${settings.tabSize} spaces`}
          icon="keyboard-tab"
          onPress={handleTabSizeChange}
        />
        <SettingItem
          title="Default Language"
          subtitle={settings.defaultLanguage.toUpperCase()}
          icon="code"
          onPress={handleDefaultLanguageChange}
        />
      </SettingSection>

      {/* Data */}
      <SettingSection title="Data">
        <SettingItem
          title="Export Settings"
          subtitle="Save settings to file"
          icon="file-download"
          onPress={handleExportSettings}
        />
        <SettingItem
          title="Import Settings"
          subtitle="Load settings from file"
          icon="file-upload"
          onPress={handleImportSettings}
        />
        <SettingItem
          title="Reset Settings"
          subtitle="Restore default settings"
          icon="restore"
          onPress={handleResetSettings}
        />
      </SettingSection>

      {/* About */}
      <SettingSection title="About">
        <SettingItem
          title="About"
          subtitle="Version 1.0.0"
          icon="info"
          onPress={handleAbout}
        />
      </SettingSection>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    paddingBottom: 32,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginHorizontal: 16,
    marginBottom: 8,
  },
  sectionContent: {
    marginHorizontal: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  settingIcon: {
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
});

export default SettingsScreen;
