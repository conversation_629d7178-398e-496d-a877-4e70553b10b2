import React from 'react';
import { View, StyleSheet, StatusBar } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { useTheme } from '../contexts/ThemeContext';
import EditorScreen from './EditorScreen';
import TerminalScreen from './TerminalScreen';
import ExplorerScreen from './ExplorerScreen';
import SettingsScreen from './SettingsScreen';

const Tab = createBottomTabNavigator();

const MainScreen: React.FC = () => {
  const { theme, isDark } = useTheme();

  return (
    <SafeAreaProvider>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.background}
      />
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <NavigationContainer>
          <Tab.Navigator
            screenOptions={({ route }) => ({
              tabBarIcon: ({ focused, color, size }) => {
                let iconName: string;

                switch (route.name) {
                  case 'Editor':
                    iconName = 'edit';
                    break;
                  case 'Terminal':
                    iconName = 'terminal';
                    break;
                  case 'Explorer':
                    iconName = 'folder';
                    break;
                  case 'Settings':
                    iconName = 'settings';
                    break;
                  default:
                    iconName = 'help';
                }

                return <Icon name={iconName} size={size} color={color} />;
              },
              tabBarActiveTintColor: theme.colors.primary,
              tabBarInactiveTintColor: theme.colors.textSecondary,
              tabBarStyle: {
                backgroundColor: theme.colors.surface,
                borderTopColor: theme.colors.border,
                borderTopWidth: 1,
              },
              headerStyle: {
                backgroundColor: theme.colors.surface,
                borderBottomColor: theme.colors.border,
                borderBottomWidth: 1,
              },
              headerTintColor: theme.colors.text,
              headerTitleStyle: {
                fontWeight: theme.typography.fontWeight.medium,
                fontSize: theme.typography.fontSize.lg,
              },
            })}
          >
            <Tab.Screen 
              name="Editor" 
              component={EditorScreen}
              options={{ title: 'Editor' }}
            />
            <Tab.Screen 
              name="Terminal" 
              component={TerminalScreen}
              options={{ title: 'Terminal' }}
            />
            <Tab.Screen 
              name="Explorer" 
              component={ExplorerScreen}
              options={{ title: 'Explorer' }}
            />
            <Tab.Screen 
              name="Settings" 
              component={SettingsScreen}
              options={{ title: 'Settings' }}
            />
          </Tab.Navigator>
        </NavigationContainer>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default MainScreen;
