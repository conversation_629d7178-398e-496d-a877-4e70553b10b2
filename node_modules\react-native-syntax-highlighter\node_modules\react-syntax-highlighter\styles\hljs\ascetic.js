"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "white",
        "color": "black"
    },
    "hljs-string": {
        "color": "#888"
    },
    "hljs-variable": {
        "color": "#888"
    },
    "hljs-template-variable": {
        "color": "#888"
    },
    "hljs-symbol": {
        "color": "#888"
    },
    "hljs-bullet": {
        "color": "#888"
    },
    "hljs-section": {
        "color": "#888",
        "fontWeight": "bold"
    },
    "hljs-addition": {
        "color": "#888"
    },
    "hljs-attribute": {
        "color": "#888"
    },
    "hljs-link": {
        "color": "#888"
    },
    "hljs-comment": {
        "color": "#ccc"
    },
    "hljs-quote": {
        "color": "#ccc"
    },
    "hljs-meta": {
        "color": "#ccc"
    },
    "hljs-deletion": {
        "color": "#ccc"
    },
    "hljs-keyword": {
        "fontWeight": "bold"
    },
    "hljs-selector-tag": {
        "fontWeight": "bold"
    },
    "hljs-name": {
        "fontWeight": "bold"
    },
    "hljs-type": {
        "fontWeight": "bold"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    }
};