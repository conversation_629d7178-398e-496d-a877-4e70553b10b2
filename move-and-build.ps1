#!/usr/bin/env pwsh

Write-Host "========================================" -ForegroundColor Green
Write-Host "Moving Project and Building APK" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Define paths
$currentPath = Get-Location
$shortPath = "C:\VLS"
$projectName = "VisualLabStudio"

Write-Host "[1/6] Creating short path directory..." -ForegroundColor Yellow
if (Test-Path $shortPath) {
    Remove-Item $shortPath -Recurse -Force
}
New-Item -ItemType Directory -Path $shortPath -Force | Out-Null

Write-Host "[2/6] Copying project to short path..." -ForegroundColor Yellow
Write-Host "From: $currentPath" -ForegroundColor Cyan
Write-Host "To: $shortPath\$projectName" -ForegroundColor Cyan

# Copy project files (excluding node_modules and build folders)
$excludeItems = @("node_modules", "android\build", "android\app\build", ".git")
$items = Get-ChildItem -Path $currentPath -Force | Where-Object { 
    $_.Name -notin $excludeItems 
}

foreach ($item in $items) {
    Copy-Item -Path $item.FullName -Destination "$shortPath\$projectName" -Recurse -Force
    Write-Host "Copied: $($item.Name)" -ForegroundColor Gray
}

Write-Host "[3/6] Changing to new directory..." -ForegroundColor Yellow
Set-Location "$shortPath\$projectName"

Write-Host "[4/6] Setting up Android environment..." -ForegroundColor Yellow
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:ANDROID_SDK_ROOT = "C:\Users\<USER>\AppData\Local\Android\Sdk"

Write-Host "[5/6] Installing dependencies..." -ForegroundColor Yellow
npm install

Write-Host "[6/6] Building APK..." -ForegroundColor Yellow
Set-Location android

# Build APK
.\gradlew assembleDebug

if ($LASTEXITCODE -eq 0) {
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "BUILD SUCCESSFUL!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    
    $apkPath = "app\build\outputs\apk\debug\app-debug.apk"
    if (Test-Path $apkPath) {
        $fullPath = Resolve-Path $apkPath
        Write-Host "APK Location: $fullPath" -ForegroundColor Cyan
        
        # Copy APK to original directory
        Copy-Item $apkPath "$currentPath\VisualLabStudio-debug.apk" -Force
        Write-Host "APK copied to original directory: VisualLabStudio-debug.apk" -ForegroundColor Cyan
        
        # Show APK info
        $apkSize = (Get-Item $apkPath).Length / 1MB
        Write-Host "APK Size: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Cyan
        
        Write-Host "" -ForegroundColor Green
        Write-Host "🎉 SUCCESS! APK generated successfully!" -ForegroundColor Green
        Write-Host "📱 Install the APK on your Android device to test the app." -ForegroundColor Green
        
    } else {
        Write-Host "WARNING: APK file not found at expected location" -ForegroundColor Yellow
    }
} else {
    Write-Host "BUILD FAILED!" -ForegroundColor Red
    exit 1
}

Set-Location $currentPath
Write-Host "Build process completed!" -ForegroundColor Green
