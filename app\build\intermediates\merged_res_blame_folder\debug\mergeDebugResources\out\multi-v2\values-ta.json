{"logs": [{"outputFile": "com.visuallabstudio.ide.app-mergeDebugResources-53:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3fdae449ec0975b3ed67992fb9c55c3c\\transformed\\jetified-ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1030,1109,1191,1277,1367,1447,1516", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1025,1104,1186,1272,1362,1442,1511,1631"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1338,1435,5358,5452,5627,5801,5884,5993,6084,6179,6259,6338,6420,6590,6972,7052,7121", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "1430,1514,5447,5548,5713,5879,5988,6079,6174,6254,6333,6415,6501,6675,7047,7116,7236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8894ffd5200146a245d974f5f73061d6\\transformed\\core-1.12.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "592,688,791,890,988,1095,1210,6762", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "683,786,885,983,1090,1205,1333,6858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f063da582707269a21bd980d1f8ebd44\\transformed\\jetified-material3-1.1.2\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,420,542,626,718,828,975,1102,1239,1319,1419,1521,1625,1753,1872,1977,2119,2259,2389,2591,2716,2833,2955,3098,3201,3295,3434,3566,3668,3777,3880,4021,4169,4274,4381,4455,4538,4622,4704,4813,4889,4973,5073,5189,5280,5381,5466,5584,5684,5786,5915,5991,6098", "endColumns": "123,123,116,121,83,91,109,146,126,136,79,99,101,103,127,118,104,141,139,129,201,124,116,121,142,102,93,138,131,101,108,102,140,147,104,106,73,82,83,81,108,75,83,99,115,90,100,84,117,99,101,128,75,106,97", "endOffsets": "174,298,415,537,621,713,823,970,1097,1234,1314,1414,1516,1620,1748,1867,1972,2114,2254,2384,2586,2711,2828,2950,3093,3196,3290,3429,3561,3663,3772,3875,4016,4164,4269,4376,4450,4533,4617,4699,4808,4884,4968,5068,5184,5275,5376,5461,5579,5679,5781,5910,5986,6093,6191"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,229,353,470,1519,1603,1695,1805,1952,2079,2216,2296,2396,2498,2602,2730,2849,2954,3096,3236,3366,3568,3693,3810,3932,4075,4178,4272,4411,4543,4645,4754,4857,4998,5146,5251,5553,5718,6506,6680,6863,7241,7317,7401,7501,7617,7708,7809,7894,8012,8112,8214,8343,8419,8526", "endColumns": "123,123,116,121,83,91,109,146,126,136,79,99,101,103,127,118,104,141,139,129,201,124,116,121,142,102,93,138,131,101,108,102,140,147,104,106,73,82,83,81,108,75,83,99,115,90,100,84,117,99,101,128,75,106,97", "endOffsets": "224,348,465,587,1598,1690,1800,1947,2074,2211,2291,2391,2493,2597,2725,2844,2949,3091,3231,3361,3563,3688,3805,3927,4070,4173,4267,4406,4538,4640,4749,4852,4993,5141,5246,5353,5622,5796,6585,6757,6967,7312,7396,7496,7612,7703,7804,7889,8007,8107,8209,8338,8414,8521,8619"}}]}]}