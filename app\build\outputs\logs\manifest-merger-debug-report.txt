-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:2:1-54:12
INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:2:1-54:12
INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:2:1-54:12
INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:2:1-54:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f063da582707269a21bd980d1f8ebd44\transformed\jetified-material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1d196cc559026ab2c95449eecdf0007e\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd965f428ebf374b343bcfc833b5b6c2\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8008fb8d1b73c433a91bdaa3ea8b749d\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5690ee40938706511c8eb5941d85a5ac\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\aa3a419b0a20fc854f2a6d805962e11d\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4781f7728801d906d8349c8710dd6534\transformed\jetified-accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3408a827c1384f8f5073ea350bc57a58\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6cc9282e9b0626679eb80181d5f1ac0f\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8f22cc0e2f3115048b73aca362586175\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-pager-indicators:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\43495c8a0b2527a24bb0d9cdcb9d2f4c\transformed\jetified-accompanist-pager-indicators-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cbce3822cfb153a16ba37953b7e5f931\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d421573b95e70e9b185561dc8c1d1db0\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfbeccdae474945deb46e0107e8e4b9c\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ea7643a32c96256d0616405df2558c4\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\05310c5e8fdcbdaff8ee1cc0b3238193\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-pager:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d518d5c803df3e7b22e615d07a55b6aa\transformed\jetified-accompanist-pager-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [dev.chrisbanes.snapper:snapper:0.2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5cbd7d38c364be387edfc09417dd8d56\transformed\jetified-snapper-0.2.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f285eb716d8adc10800d61b46472ffea\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9969c3178ff477d6e5a31f2b33dc5d23\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a486f272594689853c6842378f8c01fc\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19c7e6f74ff26295b636cce276e22695\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\375c6dc48b050fce721453ff7ffe46b6\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5711657455d05c4660b673c19740cde8\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\64d2404abb66fba146de8efc9019b015\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eaeca2904bc1390a09f4142567b38429\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\53be92c108a5db7f6276d8e4437bfe65\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\57192046d19f0c7582f609f3c6fe351c\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0da7af08cb8885dd99dee5a4f49260e4\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\334f86b046e8270525b8642678b54eb7\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bde4f1c348ea8c3398d10bae68475328\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f294926cd3dabe422702a62f960477f2\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4436efcc48faff3815a185aab2a94eff\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eeb05a6011deff7b969eea9b75c103ca\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\397f527abf8fde0725131a178b7204d6\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ec034867c8c0afa7cb22bf3b37e37b9c\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3fdae449ec0975b3ed67992fb9c55c3c\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e29889ba9e6b5bc222fd6b82c5e3bd06\transformed\jetified-ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dcfa482e1f8f37eaea1279c363b6a605\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6a1479560cd7bee0df2f65125161ceb\transformed\jetified-activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\889afdfd5564809fb45a969c5977c8cf\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bdd98b1f4f7a388847fad8484e1435e0\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\69edff8416db44addfd5ce6851b64ff9\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\100bfb58a21d382863864bf88dbdbad1\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66cd74ee7634a48fdf4bc7c6a9b0e681\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a6541071dde422f702638c130ee542f\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bb4a8908a67405e8bcd7744e0f7f37e7\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fe71e1969262d9bd4587ddd96320371e\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8fae38aab629e85ee0c34a31e8731ef9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dd71aa0fabc415221a67c056d56dddc2\transformed\jetified-napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c61a2f43b5cc718c2119677c13b1922b\transformed\documentfile-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0c33b23315959d5cfb07f5d37b7fcae9\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\97298d814960cf33118dbeaa037c11dc\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\34fa943652da8fddb09f5fcab90a59e3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df75aa23dc9991d65e81a3e17b48ef61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b7222b2bca4bc270b26da7b2f15d141\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:5:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:5:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:6:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:6:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:7:5-8:40
	tools:ignore
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:8:9-37
	android:name
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:7:22-79
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:9:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:9:22-64
application
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:11:5-53:19
INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:11:5-53:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e29889ba9e6b5bc222fd6b82c5e3bd06\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e29889ba9e6b5bc222fd6b82c5e3bd06\transformed\jetified-ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dcfa482e1f8f37eaea1279c363b6a605\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dcfa482e1f8f37eaea1279c363b6a605\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\34fa943652da8fddb09f5fcab90a59e3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\34fa943652da8fddb09f5fcab90a59e3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df75aa23dc9991d65e81a3e17b48ef61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df75aa23dc9991d65e81a3e17b48ef61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:20:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:18:9-35
	android:label
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:16:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:14:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:17:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:21:9-29
	android:icon
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:15:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:12:9-35
	android:theme
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:19:9-56
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:13:9-65
activity#com.visuallabstudio.ide.MainActivity
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:23:9-52:20
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:27:13-55
	android:exported
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:26:13-60
	android:name
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:24:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:28:13-31:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:30:17-77
	android:name
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:30:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:mimeType:text/*
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:34:13-39:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:35:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:35:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:36:17-76
	android:name
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:36:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:37:17-78
	android:name
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:37:27-75
data
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:38:17-51
	android:pathPattern
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:45:23-52
	android:mimeType
		ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:38:23-48
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:pathPattern:.*\\.css+data:pathPattern:.*\\.html+data:pathPattern:.*\\.js+data:pathPattern:.*\\.json+data:pathPattern:.*\\.py+data:pathPattern:.*\\.xml
ADDED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:41:13-51:29
uses-sdk
INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f063da582707269a21bd980d1f8ebd44\transformed\jetified-material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f063da582707269a21bd980d1f8ebd44\transformed\jetified-material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1d196cc559026ab2c95449eecdf0007e\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1d196cc559026ab2c95449eecdf0007e\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd965f428ebf374b343bcfc833b5b6c2\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd965f428ebf374b343bcfc833b5b6c2\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8008fb8d1b73c433a91bdaa3ea8b749d\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8008fb8d1b73c433a91bdaa3ea8b749d\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5690ee40938706511c8eb5941d85a5ac\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5690ee40938706511c8eb5941d85a5ac\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\aa3a419b0a20fc854f2a6d805962e11d\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\aa3a419b0a20fc854f2a6d805962e11d\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4781f7728801d906d8349c8710dd6534\transformed\jetified-accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4781f7728801d906d8349c8710dd6534\transformed\jetified-accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3408a827c1384f8f5073ea350bc57a58\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3408a827c1384f8f5073ea350bc57a58\transformed\jetified-activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6cc9282e9b0626679eb80181d5f1ac0f\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6cc9282e9b0626679eb80181d5f1ac0f\transformed\jetified-activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8f22cc0e2f3115048b73aca362586175\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8f22cc0e2f3115048b73aca362586175\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-pager-indicators:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\43495c8a0b2527a24bb0d9cdcb9d2f4c\transformed\jetified-accompanist-pager-indicators-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-pager-indicators:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\43495c8a0b2527a24bb0d9cdcb9d2f4c\transformed\jetified-accompanist-pager-indicators-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cbce3822cfb153a16ba37953b7e5f931\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cbce3822cfb153a16ba37953b7e5f931\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d421573b95e70e9b185561dc8c1d1db0\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d421573b95e70e9b185561dc8c1d1db0\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfbeccdae474945deb46e0107e8e4b9c\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfbeccdae474945deb46e0107e8e4b9c\transformed\jetified-material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ea7643a32c96256d0616405df2558c4\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ea7643a32c96256d0616405df2558c4\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\05310c5e8fdcbdaff8ee1cc0b3238193\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\05310c5e8fdcbdaff8ee1cc0b3238193\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-pager:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d518d5c803df3e7b22e615d07a55b6aa\transformed\jetified-accompanist-pager-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-pager:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d518d5c803df3e7b22e615d07a55b6aa\transformed\jetified-accompanist-pager-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [dev.chrisbanes.snapper:snapper:0.2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5cbd7d38c364be387edfc09417dd8d56\transformed\jetified-snapper-0.2.2\AndroidManifest.xml:20:5-22:41
MERGED from [dev.chrisbanes.snapper:snapper:0.2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5cbd7d38c364be387edfc09417dd8d56\transformed\jetified-snapper-0.2.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f285eb716d8adc10800d61b46472ffea\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f285eb716d8adc10800d61b46472ffea\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9969c3178ff477d6e5a31f2b33dc5d23\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9969c3178ff477d6e5a31f2b33dc5d23\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a486f272594689853c6842378f8c01fc\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a486f272594689853c6842378f8c01fc\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19c7e6f74ff26295b636cce276e22695\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19c7e6f74ff26295b636cce276e22695\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\375c6dc48b050fce721453ff7ffe46b6\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\375c6dc48b050fce721453ff7ffe46b6\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5711657455d05c4660b673c19740cde8\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5711657455d05c4660b673c19740cde8\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\64d2404abb66fba146de8efc9019b015\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\64d2404abb66fba146de8efc9019b015\transformed\jetified-ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eaeca2904bc1390a09f4142567b38429\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eaeca2904bc1390a09f4142567b38429\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\53be92c108a5db7f6276d8e4437bfe65\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\53be92c108a5db7f6276d8e4437bfe65\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\57192046d19f0c7582f609f3c6fe351c\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\57192046d19f0c7582f609f3c6fe351c\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0da7af08cb8885dd99dee5a4f49260e4\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0da7af08cb8885dd99dee5a4f49260e4\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\334f86b046e8270525b8642678b54eb7\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\334f86b046e8270525b8642678b54eb7\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bde4f1c348ea8c3398d10bae68475328\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bde4f1c348ea8c3398d10bae68475328\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f294926cd3dabe422702a62f960477f2\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f294926cd3dabe422702a62f960477f2\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4436efcc48faff3815a185aab2a94eff\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4436efcc48faff3815a185aab2a94eff\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eeb05a6011deff7b969eea9b75c103ca\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eeb05a6011deff7b969eea9b75c103ca\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\397f527abf8fde0725131a178b7204d6\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\397f527abf8fde0725131a178b7204d6\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ec034867c8c0afa7cb22bf3b37e37b9c\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ec034867c8c0afa7cb22bf3b37e37b9c\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3fdae449ec0975b3ed67992fb9c55c3c\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3fdae449ec0975b3ed67992fb9c55c3c\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e29889ba9e6b5bc222fd6b82c5e3bd06\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e29889ba9e6b5bc222fd6b82c5e3bd06\transformed\jetified-ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dcfa482e1f8f37eaea1279c363b6a605\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dcfa482e1f8f37eaea1279c363b6a605\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6a1479560cd7bee0df2f65125161ceb\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6a1479560cd7bee0df2f65125161ceb\transformed\jetified-activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\889afdfd5564809fb45a969c5977c8cf\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\889afdfd5564809fb45a969c5977c8cf\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bdd98b1f4f7a388847fad8484e1435e0\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bdd98b1f4f7a388847fad8484e1435e0\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\69edff8416db44addfd5ce6851b64ff9\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\69edff8416db44addfd5ce6851b64ff9\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\100bfb58a21d382863864bf88dbdbad1\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\100bfb58a21d382863864bf88dbdbad1\transformed\jetified-room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66cd74ee7634a48fdf4bc7c6a9b0e681\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66cd74ee7634a48fdf4bc7c6a9b0e681\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a6541071dde422f702638c130ee542f\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a6541071dde422f702638c130ee542f\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bb4a8908a67405e8bcd7744e0f7f37e7\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bb4a8908a67405e8bcd7744e0f7f37e7\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fe71e1969262d9bd4587ddd96320371e\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fe71e1969262d9bd4587ddd96320371e\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8fae38aab629e85ee0c34a31e8731ef9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8fae38aab629e85ee0c34a31e8731ef9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dd71aa0fabc415221a67c056d56dddc2\transformed\jetified-napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dd71aa0fabc415221a67c056d56dddc2\transformed\jetified-napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c61a2f43b5cc718c2119677c13b1922b\transformed\documentfile-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c61a2f43b5cc718c2119677c13b1922b\transformed\documentfile-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0c33b23315959d5cfb07f5d37b7fcae9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0c33b23315959d5cfb07f5d37b7fcae9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\97298d814960cf33118dbeaa037c11dc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\97298d814960cf33118dbeaa037c11dc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\34fa943652da8fddb09f5fcab90a59e3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\34fa943652da8fddb09f5fcab90a59e3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df75aa23dc9991d65e81a3e17b48ef61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df75aa23dc9991d65e81a3e17b48ef61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b7222b2bca4bc270b26da7b2f15d141\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9b7222b2bca4bc270b26da7b2f15d141\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df75aa23dc9991d65e81a3e17b48ef61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df75aa23dc9991d65e81a3e17b48ef61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e29889ba9e6b5bc222fd6b82c5e3bd06\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e29889ba9e6b5bc222fd6b82c5e3bd06\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e29889ba9e6b5bc222fd6b82c5e3bd06\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dcfa482e1f8f37eaea1279c363b6a605\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dcfa482e1f8f37eaea1279c363b6a605\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dcfa482e1f8f37eaea1279c363b6a605\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.visuallabstudio.ide.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.visuallabstudio.ide.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
