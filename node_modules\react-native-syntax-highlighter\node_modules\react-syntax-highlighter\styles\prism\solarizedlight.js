"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = {
    "code[class*=\"language-\"]": {
        "color": "#657b83",
        "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "wordWrap": "normal",
        "lineHeight": "1.5",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none"
    },
    "pre[class*=\"language-\"]": {
        "color": "#657b83",
        "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "wordWrap": "normal",
        "lineHeight": "1.5",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "padding": "1em",
        "margin": ".5em 0",
        "overflow": "auto",
        "borderRadius": "0.3em",
        "backgroundColor": "#fdf6e3"
    },
    "pre[class*=\"language-\"]::-moz-selection": {
        "background": "#073642"
    },
    "pre[class*=\"language-\"] ::-moz-selection": {
        "background": "#073642"
    },
    "code[class*=\"language-\"]::-moz-selection": {
        "background": "#073642"
    },
    "code[class*=\"language-\"] ::-moz-selection": {
        "background": "#073642"
    },
    "pre[class*=\"language-\"]::selection": {
        "background": "#073642"
    },
    "pre[class*=\"language-\"] ::selection": {
        "background": "#073642"
    },
    "code[class*=\"language-\"]::selection": {
        "background": "#073642"
    },
    "code[class*=\"language-\"] ::selection": {
        "background": "#073642"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "backgroundColor": "#fdf6e3",
        "padding": ".1em",
        "borderRadius": ".3em"
    },
    "comment": {
        "color": "#93a1a1"
    },
    "prolog": {
        "color": "#93a1a1"
    },
    "doctype": {
        "color": "#93a1a1"
    },
    "cdata": {
        "color": "#93a1a1"
    },
    "punctuation": {
        "color": "#586e75"
    },
    ".namespace": {
        "Opacity": ".7"
    },
    "property": {
        "color": "#268bd2"
    },
    "tag": {
        "color": "#268bd2"
    },
    "boolean": {
        "color": "#268bd2"
    },
    "number": {
        "color": "#268bd2"
    },
    "constant": {
        "color": "#268bd2"
    },
    "symbol": {
        "color": "#268bd2"
    },
    "deleted": {
        "color": "#268bd2"
    },
    "selector": {
        "color": "#2aa198"
    },
    "attr-name": {
        "color": "#2aa198"
    },
    "string": {
        "color": "#2aa198"
    },
    "char": {
        "color": "#2aa198"
    },
    "builtin": {
        "color": "#2aa198"
    },
    "url": {
        "color": "#2aa198"
    },
    "inserted": {
        "color": "#2aa198"
    },
    "entity": {
        "color": "#657b83",
        "background": "#eee8d5",
        "cursor": "help"
    },
    "atrule": {
        "color": "#859900"
    },
    "attr-value": {
        "color": "#859900"
    },
    "keyword": {
        "color": "#859900"
    },
    "function": {
        "color": "#b58900"
    },
    "regex": {
        "color": "#cb4b16"
    },
    "important": {
        "color": "#cb4b16",
        "fontWeight": "bold"
    },
    "variable": {
        "color": "#cb4b16"
    },
    "bold": {
        "fontWeight": "bold"
    },
    "italic": {
        "fontStyle": "italic"
    }
};