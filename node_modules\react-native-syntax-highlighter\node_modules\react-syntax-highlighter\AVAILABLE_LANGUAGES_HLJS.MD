## Available `language` imports 
* oneC (1c)
* abnf
* accesslog
* actionscript
* ada
* apache
* applescript
* arduino
* armasm
* asciidoc
* aspectj
* autohotkey
* autoit
* avrasm
* awk
* axapta
* bash
* basic
* bnf
* brainfuck
* cal
* capnproto
* ceylon
* clean
* clojureRepl (clojure-repl)
* clojure
* cmake
* coffeescript
* coq
* cos
* cpp
* crmsh
* crystal
* cs
* csp
* css
* d
* dart
* delphi
* diff
* django
* dns
* dockerfile
* dos
* dsconfig
* dts
* dust
* ebnf
* elixir
* elm
* erb
* erlangRepl (erlang-repl)
* erlang
* excel
* fix
* flix
* fortran
* fsharp
* gams
* gauss
* gcode
* gherkin
* glsl
* go
* golo
* gradle
* groovy
* haml
* handlebars
* haskell
* haxe
* hsp
* htmlbars
* http
* hy
* inform7
* ini
* irpf90
* java
* javascript
* jbossCli (jboss-cli)
* json
* julia<PERSON><PERSON>l (julia-repl)
* julia
* kotlin
* lasso
* ldif
* leaf
* less
* lisp
* livecodeserver
* livescript
* llvm
* lsl
* lua
* makefile
* markdown
* mathematica
* matlab
* maxima
* mel
* mercury
* mipsasm
* mizar
* mojolicious
* monkey
* moonscript
* n1ql
* nginx
* nimrod
* nix
* nsis
* objectivec
* ocaml
* openscad
* oxygene
* parser3
* perl
* pf
* php
* pony
* powershell
* processing
* profile
* prolog
* protobuf
* puppet
* purebasic
* python
* q
* qml
* r
* rib
* roboconf
* routeros
* rsl
* ruby
* ruleslanguage
* rust
* scala
* scheme
* scilab
* scss
* shell
* smali
* smalltalk
* sml
* sqf
* sql
* stan
* stata
* step21
* stylus
* subunit
* swift
* taggerscript
* tap
* tcl
* tex
* thrift
* tp
* twig
* typescript
* vala
* vbnet
* vbscriptHtml (vbscript-html)
* vbscript
* verilog
* vhdl
* vim
* x86asm
* xl
* xml
* xquery
* yaml
* zephir