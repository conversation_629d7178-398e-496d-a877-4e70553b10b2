import React, { useState, useRef } from 'react';
import {
  View,
  TextInput,
  ScrollView,
  StyleSheet,
  Text,
  Dimensions,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { Language } from '../types';

interface CodeEditorProps {
  value: string;
  onChangeText: (text: string) => void;
  language: Language;
  placeholder?: string;
  editable?: boolean;
  showLineNumbers?: boolean;
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  onChangeText,
  language,
  placeholder = 'Start typing your code here...',
  editable = true,
  showLineNumbers = true,
}) => {
  const { theme } = useTheme();
  const [scrollOffset, setScrollOffset] = useState(0);
  const textInputRef = useRef<TextInput>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  // Get syntax highlighting colors based on language
  const getSyntaxColors = () => {
    return {
      keyword: '#FF79C6',      // Pink for keywords
      string: '#F1FA8C',       // Yellow for strings
      comment: '#6272A4',      // Blue-gray for comments
      number: '#BD93F9',       // Purple for numbers
      function: '#50FA7B',     // Green for functions
      operator: '#FFB86C',     // Orange for operators
      default: theme.colors.text,
    };
  };

  // Simple syntax highlighting for JavaScript
  const highlightJavaScript = (code: string) => {
    const colors = getSyntaxColors();
    const keywords = [
      'const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while',
      'do', 'switch', 'case', 'break', 'continue', 'try', 'catch', 'finally',
      'throw', 'new', 'this', 'class', 'extends', 'import', 'export', 'default',
      'async', 'await', 'true', 'false', 'null', 'undefined'
    ];

    let highlightedCode = code;
    
    // Highlight keywords
    keywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      highlightedCode = highlightedCode.replace(regex, `<keyword>${keyword}</keyword>`);
    });

    // Highlight strings
    highlightedCode = highlightedCode.replace(
      /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
      '<string>$1$2$1</string>'
    );

    // Highlight comments
    highlightedCode = highlightedCode.replace(
      /\/\/.*$/gm,
      '<comment>$&</comment>'
    );
    highlightedCode = highlightedCode.replace(
      /\/\*[\s\S]*?\*\//g,
      '<comment>$&</comment>'
    );

    // Highlight numbers
    highlightedCode = highlightedCode.replace(
      /\b\d+\.?\d*\b/g,
      '<number>$&</number>'
    );

    return highlightedCode;
  };

  // Simple syntax highlighting for Python
  const highlightPython = (code: string) => {
    const colors = getSyntaxColors();
    const keywords = [
      'def', 'class', 'if', 'elif', 'else', 'for', 'while', 'try', 'except',
      'finally', 'with', 'as', 'import', 'from', 'return', 'yield', 'break',
      'continue', 'pass', 'and', 'or', 'not', 'in', 'is', 'True', 'False',
      'None', 'lambda', 'global', 'nonlocal'
    ];

    let highlightedCode = code;
    
    // Highlight keywords
    keywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      highlightedCode = highlightedCode.replace(regex, `<keyword>${keyword}</keyword>`);
    });

    // Highlight strings
    highlightedCode = highlightedCode.replace(
      /(["'])((?:\\.|(?!\1)[^\\])*?)\1/g,
      '<string>$1$2$1</string>'
    );

    // Highlight comments
    highlightedCode = highlightedCode.replace(
      /#.*$/gm,
      '<comment>$&</comment>'
    );

    // Highlight numbers
    highlightedCode = highlightedCode.replace(
      /\b\d+\.?\d*\b/g,
      '<number>$&</number>'
    );

    return highlightedCode;
  };

  // Apply syntax highlighting based on language
  const applySyntaxHighlighting = (code: string) => {
    switch (language.toLowerCase()) {
      case 'javascript':
      case 'js':
        return highlightJavaScript(code);
      case 'python':
      case 'py':
        return highlightPython(code);
      default:
        return code;
    }
  };

  // Convert highlighted code to React components
  const renderHighlightedCode = (highlightedCode: string) => {
    const colors = getSyntaxColors();
    const parts = highlightedCode.split(/(<\/?(?:keyword|string|comment|number|function|operator)>)/);
    let currentTag = '';
    
    return parts.map((part, index) => {
      if (part.startsWith('<') && part.endsWith('>')) {
        if (part.startsWith('</')) {
          currentTag = '';
          return null;
        } else {
          currentTag = part.slice(1, -1);
          return null;
        }
      }
      
      if (!part) return null;
      
      const color = currentTag ? colors[currentTag as keyof typeof colors] || colors.default : colors.default;
      
      return (
        <Text key={index} style={{ color }}>
          {part}
        </Text>
      );
    }).filter(Boolean);
  };

  // Generate line numbers
  const generateLineNumbers = () => {
    const lines = value.split('\n');
    return lines.map((_, index) => (
      <Text
        key={index}
        style={[
          styles.lineNumber,
          {
            color: theme.colors.textSecondary,
            fontSize: theme.typography.fontSize.sm,
          }
        ]}
      >
        {index + 1}
      </Text>
    ));
  };

  const handleScroll = (event: any) => {
    const { contentOffset } = event.nativeEvent;
    setScrollOffset(contentOffset.y);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollContainer}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={true}
      >
        <View style={styles.editorContainer}>
          {/* Line Numbers */}
          {showLineNumbers && (
            <View style={[styles.lineNumberContainer, { backgroundColor: theme.colors.surface }]}>
              {generateLineNumbers()}
            </View>
          )}

          {/* Code Input */}
          <View style={styles.codeContainer}>
            {/* Syntax Highlighted Background */}
            <View style={styles.highlightedBackground} pointerEvents="none">
              <Text style={[styles.codeText, { color: 'transparent' }]}>
                {renderHighlightedCode(applySyntaxHighlighting(value || ' '))}
              </Text>
            </View>

            {/* Actual TextInput */}
            <TextInput
              ref={textInputRef}
              style={[
                styles.codeInput,
                {
                  color: theme.colors.text,
                  fontSize: theme.typography.fontSize.sm,
                }
              ]}
              value={value}
              onChangeText={onChangeText}
              multiline
              placeholder={placeholder}
              placeholderTextColor={theme.colors.textSecondary}
              textAlignVertical="top"
              autoCapitalize="none"
              autoCorrect={false}
              spellCheck={false}
              keyboardType="ascii-capable"
              editable={editable}
              scrollEnabled={false}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  editorContainer: {
    flexDirection: 'row',
    minHeight: '100%',
  },
  lineNumberContainer: {
    paddingHorizontal: 8,
    paddingVertical: 16,
    borderRightWidth: 1,
    borderRightColor: '#38383A',
    minWidth: 50,
  },
  lineNumber: {
    textAlign: 'right',
    lineHeight: 20,
    fontFamily: 'monospace',
  },
  codeContainer: {
    flex: 1,
    position: 'relative',
  },
  highlightedBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    padding: 16,
    zIndex: 1,
  },
  codeInput: {
    flex: 1,
    padding: 16,
    fontFamily: 'monospace',
    lineHeight: 20,
    minHeight: '100%',
    zIndex: 2,
  },
  codeText: {
    fontFamily: 'monospace',
    lineHeight: 20,
  },
});

export default CodeEditor;
