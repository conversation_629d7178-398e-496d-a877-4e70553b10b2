1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.visuallabstudio.ide"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
11-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:5:5-80
11-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:5:22-77
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:6:5-81
12-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:7:5-8:40
13-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:7:22-79
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:9:5-67
14-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:9:22-64
15
16    <permission
16-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.visuallabstudio.ide.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.visuallabstudio.ide.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:11:5-53:19
23        android:allowBackup="true"
23-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:12:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8894ffd5200146a245d974f5f73061d6\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:13:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:14:9-54
29        android:icon="@mipmap/ic_launcher"
29-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:15:9-43
30        android:label="@string/app_name"
30-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:16:9-41
31        android:requestLegacyExternalStorage="true"
31-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:20:9-52
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:17:9-54
33        android:supportsRtl="true"
33-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:18:9-35
34        android:theme="@style/Theme.VisualLabStudioIDE" >
34-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:19:9-56
35        <activity
35-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:23:9-52:20
36            android:name="com.visuallabstudio.ide.MainActivity"
36-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:24:13-41
37            android:exported="true"
37-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:25:13-36
38            android:theme="@style/Theme.VisualLabStudioIDE"
38-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:26:13-60
39            android:windowSoftInputMode="adjustResize" >
39-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:27:13-55
40            <intent-filter>
40-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:28:13-31:29
41                <action android:name="android.intent.action.MAIN" />
41-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:29:17-69
41-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:29:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:30:17-77
43-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:30:27-74
44            </intent-filter>
45
46            <!-- File opening support -->
47            <intent-filter>
47-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:34:13-39:29
48                <action android:name="android.intent.action.VIEW" />
48-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:35:17-69
48-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:35:25-66
49
50                <category android:name="android.intent.category.DEFAULT" />
50-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:36:17-76
50-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:36:27-73
51                <category android:name="android.intent.category.BROWSABLE" />
51-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:37:17-78
51-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:37:27-75
52
53                <data android:mimeType="text/*" />
53-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:38:17-51
53-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:38:23-48
54            </intent-filter>
55            <intent-filter>
55-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:41:13-51:29
56                <action android:name="android.intent.action.VIEW" />
56-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:35:17-69
56-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:35:25-66
57
58                <category android:name="android.intent.category.DEFAULT" />
58-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:36:17-76
58-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:36:27-73
59                <category android:name="android.intent.category.BROWSABLE" />
59-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:37:17-78
59-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:37:27-75
60
61                <data android:pathPattern=".*\\.py" />
61-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:38:17-51
61-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:45:23-52
62                <data android:pathPattern=".*\\.js" />
62-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:38:17-51
62-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:45:23-52
63                <data android:pathPattern=".*\\.html" />
63-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:38:17-51
63-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:45:23-52
64                <data android:pathPattern=".*\\.css" />
64-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:38:17-51
64-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:45:23-52
65                <data android:pathPattern=".*\\.json" />
65-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:38:17-51
65-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:45:23-52
66                <data android:pathPattern=".*\\.xml" />
66-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:38:17-51
66-->C:\Users\<USER>\Documents\AI Projects\Visual Lab Studio Mobile\app\src\main\AndroidManifest.xml:45:23-52
67            </intent-filter>
68        </activity>
69
70        <provider
70-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
71            android:name="androidx.startup.InitializationProvider"
71-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:25:13-67
72            android:authorities="com.visuallabstudio.ide.androidx-startup"
72-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:26:13-68
73            android:exported="false" >
73-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:27:13-37
74            <meta-data
74-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.emoji2.text.EmojiCompatInitializer"
75-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
76                android:value="androidx.startup" />
76-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ebe15e921e0471050362dd5931552eb\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
78-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
79                android:value="androidx.startup" />
79-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bbb2ff70ac48375127d17f94a4989fd8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
80            <meta-data
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
81                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
82                android:value="androidx.startup" />
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
83        </provider>
84
85        <activity
85-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e29889ba9e6b5bc222fd6b82c5e3bd06\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
86            android:name="androidx.compose.ui.tooling.PreviewActivity"
86-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e29889ba9e6b5bc222fd6b82c5e3bd06\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
87            android:exported="true" />
87-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e29889ba9e6b5bc222fd6b82c5e3bd06\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
88        <activity
88-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dcfa482e1f8f37eaea1279c363b6a605\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
89            android:name="androidx.activity.ComponentActivity"
89-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dcfa482e1f8f37eaea1279c363b6a605\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
90            android:exported="true" />
90-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dcfa482e1f8f37eaea1279c363b6a605\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
91
92        <service
92-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
93            android:name="androidx.room.MultiInstanceInvalidationService"
93-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
94            android:directBootAware="true"
94-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
95            android:exported="false" />
95-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\44007fdf905a205e1f9908b3fa184653\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
96
97        <receiver
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
98            android:name="androidx.profileinstaller.ProfileInstallReceiver"
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
99            android:directBootAware="false"
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
100            android:enabled="true"
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
101            android:exported="true"
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
102            android:permission="android.permission.DUMP" >
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
104                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
105            </intent-filter>
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
107                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
110                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
113                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\af6e7a0410744c03b3b42c564197254b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
114            </intent-filter>
115        </receiver>
116    </application>
117
118</manifest>
