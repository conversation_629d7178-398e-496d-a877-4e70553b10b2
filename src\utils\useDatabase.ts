import { useEffect, useState } from 'react';
import { DatabaseService } from '../services/DatabaseService';

export interface UseDatabaseReturn {
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
}

export const useDatabase = (): UseDatabaseReturn => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializeDatabase();
  }, []);

  const initializeDatabase = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      await DatabaseService.initialize();
      
      setIsInitialized(true);
      console.log('Database initialized successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown database error';
      setError(errorMessage);
      console.error('Failed to initialize database:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isInitialized,
    isLoading,
    error,
  };
};
