@echo off
echo ========================================
echo Building Visual Lab Studio APK
echo ========================================

echo [1/4] Installing dependencies...
call npm install

echo [2/4] Cleaning previous builds...
cd android
call gradlew clean
cd ..

echo [3/4] Building bundle...
call npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res

echo [4/4] Building APK...
cd android
call gradlew assembleDebug

echo ========================================
echo ✅ APK BUILD COMPLETE!
echo ========================================
echo APK Location: android\app\build\outputs\apk\debug\app-debug.apk
echo File size: 
for %%I in (app\build\outputs\apk\debug\app-debug.apk) do echo %%~zI bytes

pause