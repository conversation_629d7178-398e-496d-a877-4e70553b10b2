#!/usr/bin/env pwsh

Write-Host "========================================" -ForegroundColor Green
Write-Host "Building Visual Lab Studio APK (Simple)" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Set Android environment variables
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:ANDROID_SDK_ROOT = "C:\Users\<USER>\AppData\Local\Android\Sdk"

Write-Host "[1/3] Setting up environment..." -ForegroundColor Yellow
Write-Host "ANDROID_HOME: $env:ANDROID_HOME" -ForegroundColor Cyan

Write-Host "[2/3] Installing dependencies..." -ForegroundColor Yellow
npm install

Write-Host "[3/3] Building APK directly..." -ForegroundColor Yellow
Set-Location android

# Build debug APK directly
Write-Host "Running gradlew assembleDebug..." -ForegroundColor Cyan
.\gradlew assembleDebug

if ($LASTEXITCODE -eq 0) {
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "BUILD SUCCESSFUL!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    
    $apkPath = "app\build\outputs\apk\debug\app-debug.apk"
    if (Test-Path $apkPath) {
        $fullPath = Resolve-Path $apkPath
        Write-Host "APK Location: $fullPath" -ForegroundColor Cyan
        
        # Copy APK to root directory
        Copy-Item $apkPath "..\VisualLabStudio-debug.apk" -Force
        Write-Host "APK copied to: VisualLabStudio-debug.apk" -ForegroundColor Cyan
        
        # Show APK info
        $apkSize = (Get-Item $apkPath).Length / 1MB
        Write-Host "APK Size: $([math]::Round($apkSize, 2)) MB" -ForegroundColor Cyan
    }
} else {
    Write-Host "BUILD FAILED!" -ForegroundColor Red
    exit 1
}

Set-Location ..
Write-Host "Build completed!" -ForegroundColor Green
