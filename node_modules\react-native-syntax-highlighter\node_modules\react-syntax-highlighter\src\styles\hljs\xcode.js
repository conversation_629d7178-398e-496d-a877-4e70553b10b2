export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#fff",
        "color": "black"
    },
    "hljs-comment": {
        "color": "#006a00"
    },
    "hljs-quote": {
        "color": "#006a00"
    },
    "hljs-keyword": {
        "color": "#aa0d91"
    },
    "hljs-selector-tag": {
        "color": "#aa0d91"
    },
    "hljs-literal": {
        "color": "#aa0d91"
    },
    "hljs-name": {
        "color": "#008"
    },
    "hljs-variable": {
        "color": "#660"
    },
    "hljs-template-variable": {
        "color": "#660"
    },
    "hljs-string": {
        "color": "#c41a16"
    },
    "hljs-regexp": {
        "color": "#080"
    },
    "hljs-link": {
        "color": "#080"
    },
    "hljs-title": {
        "color": "#1c00cf"
    },
    "hljs-tag": {
        "color": "#1c00cf"
    },
    "hljs-symbol": {
        "color": "#1c00cf"
    },
    "hljs-bullet": {
        "color": "#1c00cf"
    },
    "hljs-number": {
        "color": "#1c00cf"
    },
    "hljs-meta": {
        "color": "#1c00cf"
    },
    "hljs-section": {
        "color": "#5c2699"
    },
    "hljs-class .hljs-title": {
        "color": "#5c2699"
    },
    "hljs-type": {
        "color": "#5c2699"
    },
    "hljs-attr": {
        "color": "#5c2699"
    },
    "hljs-built_in": {
        "color": "#5c2699"
    },
    "hljs-builtin-name": {
        "color": "#5c2699"
    },
    "hljs-params": {
        "color": "#5c2699"
    },
    "hljs-attribute": {
        "color": "#000"
    },
    "hljs-subst": {
        "color": "#000"
    },
    "hljs-formula": {
        "backgroundColor": "#eee",
        "fontStyle": "italic"
    },
    "hljs-addition": {
        "backgroundColor": "#baeeba"
    },
    "hljs-deletion": {
        "backgroundColor": "#ffc8bd"
    },
    "hljs-selector-id": {
        "color": "#9b703f"
    },
    "hljs-selector-class": {
        "color": "#9b703f"
    },
    "hljs-doctag": {
        "fontWeight": "bold"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    }
}