export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#222",
        "color": "#fff"
    },
    "hljs-comment": {
        "color": "#777"
    },
    "hljs-quote": {
        "color": "#777"
    },
    "hljs-variable": {
        "color": "#ab875d"
    },
    "hljs-template-variable": {
        "color": "#ab875d"
    },
    "hljs-tag": {
        "color": "#ab875d"
    },
    "hljs-regexp": {
        "color": "#ab875d"
    },
    "hljs-meta": {
        "color": "#ab875d"
    },
    "hljs-number": {
        "color": "#ab875d"
    },
    "hljs-built_in": {
        "color": "#ab875d"
    },
    "hljs-builtin-name": {
        "color": "#ab875d"
    },
    "hljs-literal": {
        "color": "#ab875d"
    },
    "hljs-params": {
        "color": "#ab875d"
    },
    "hljs-symbol": {
        "color": "#ab875d"
    },
    "hljs-bullet": {
        "color": "#ab875d"
    },
    "hljs-link": {
        "color": "#ab875d"
    },
    "hljs-deletion": {
        "color": "#ab875d"
    },
    "hljs-section": {
        "color": "#9b869b"
    },
    "hljs-title": {
        "color": "#9b869b"
    },
    "hljs-name": {
        "color": "#9b869b"
    },
    "hljs-selector-id": {
        "color": "#9b869b"
    },
    "hljs-selector-class": {
        "color": "#9b869b"
    },
    "hljs-type": {
        "color": "#9b869b"
    },
    "hljs-attribute": {
        "color": "#9b869b"
    },
    "hljs-string": {
        "color": "#8f9c6c"
    },
    "hljs-keyword": {
        "color": "#8f9c6c"
    },
    "hljs-selector-tag": {
        "color": "#8f9c6c"
    },
    "hljs-addition": {
        "color": "#8f9c6c"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}