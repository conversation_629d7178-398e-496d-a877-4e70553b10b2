import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useFileSystem } from '../utils/useFileSystem';
import { CodeExecutionService } from '../services/CodeExecutionService';
import { TestUtils } from '../utils/testUtils';
import { ExecutionResult, Language } from '../types';

interface TerminalLine {
  id: string;
  type: 'input' | 'output' | 'error';
  content: string;
  timestamp: Date;
}

const TerminalScreen: React.FC = () => {
  const { theme } = useTheme();
  const { currentFile } = useFileSystem();
  const [lines, setLines] = useState<TerminalLine[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    // Add welcome message
    addLine('output', 'Visual Lab Studio IDE Terminal v1.0.0');
    addLine('output', 'Type "help" for available commands');
    addLine('output', '');
  }, []);

  useEffect(() => {
    // Auto-scroll to bottom when new lines are added
    scrollViewRef.current?.scrollToEnd({ animated: true });
  }, [lines]);

  const addLine = (type: 'input' | 'output' | 'error', content: string) => {
    const newLine: TerminalLine = {
      id: `line-${Date.now()}-${Math.random()}`,
      type,
      content,
      timestamp: new Date(),
    };
    setLines(prev => [...prev, newLine]);
  };

  const executeCommand = async (command: string) => {
    if (!command.trim()) return;

    // Add input line
    addLine('input', `$ ${command}`);
    setIsExecuting(true);

    try {
      const result = await processCommand(command.trim());
      
      if (result.error) {
        addLine('error', result.error);
      } else if (result.output) {
        result.output.split('\n').forEach(line => {
          addLine('output', line);
        });
      }
    } catch (error) {
      addLine('error', `Error: ${error}`);
    } finally {
      setIsExecuting(false);
    }
  };

  const processCommand = async (command: string): Promise<ExecutionResult> => {
    const startTime = Date.now();
    
    // Parse command
    const parts = command.split(' ');
    const cmd = parts[0].toLowerCase();
    const args = parts.slice(1);

    switch (cmd) {
      case 'help':
        return {
          output: `Available commands:
  help          - Show this help message
  clear         - Clear terminal
  echo <text>   - Echo text
  date          - Show current date and time
  version       - Show IDE version
  run <lang>    - Run code (js, python)
  test          - Run system tests
  perf          - Run performance test
  memory        - Show memory usage
  samples       - Create sample files
  ls            - List files (coming soon)
  pwd           - Show current directory
  whoami        - Show current user`,
          executionTime: Date.now() - startTime,
        };

      case 'clear':
        setLines([]);
        return {
          output: '',
          executionTime: Date.now() - startTime,
        };

      case 'echo':
        return {
          output: args.join(' '),
          executionTime: Date.now() - startTime,
        };

      case 'date':
        return {
          output: new Date().toString(),
          executionTime: Date.now() - startTime,
        };

      case 'version':
        return {
          output: 'Visual Lab Studio IDE v1.0.0\nReact Native Mobile IDE\nBuilt with ❤️ for developers',
          executionTime: Date.now() - startTime,
        };

      case 'run':
        const language = args[0] as Language;
        if (!language) {
          return {
            output: '',
            error: 'Usage: run <language>\nSupported languages: ' + CodeExecutionService.getSupportedLanguages().join(', '),
            executionTime: Date.now() - startTime,
          };
        }

        if (!CodeExecutionService.isLanguageSupported(language)) {
          return {
            output: '',
            error: `Language '${language}' not supported.\nSupported languages: ${CodeExecutionService.getSupportedLanguages().join(', ')}`,
            executionTime: Date.now() - startTime,
          };
        }

        if (!currentFile || !currentFile.content.trim()) {
          return {
            output: '',
            error: 'No file loaded or file is empty. Open a file in the editor first.',
            executionTime: Date.now() - startTime,
          };
        }

        // Execute the current file's content
        try {
          const validation = CodeExecutionService.validateCode(currentFile.content, language);
          if (!validation.isValid) {
            return {
              output: '',
              error: `Code validation failed: ${validation.error}`,
              executionTime: Date.now() - startTime,
            };
          }

          // Execute code synchronously for terminal (with timeout)
          CodeExecutionService.executeCodeWithTimeout(currentFile.content, language, 5000)
            .then(result => {
              if (result.error) {
                addLine('error', `Execution Error: ${result.error}`);
              } else {
                addLine('output', `Execution Output:\n${result.output || 'No output'}`);
              }
              addLine('output', `Execution completed in ${result.executionTime}ms`);
            })
            .catch(error => {
              addLine('error', `Execution failed: ${error.message || error}`);
            });

          return {
            output: `Executing ${language} code from '${currentFile.name}'...`,
            executionTime: Date.now() - startTime,
          };
        } catch (error) {
          return {
            output: '',
            error: `Failed to execute code: ${error instanceof Error ? error.message : error}`,
            executionTime: Date.now() - startTime,
          };
        }

      case 'ls':
        return {
          output: 'File listing will be implemented soon!',
          executionTime: Date.now() - startTime,
        };

      case 'pwd':
        return {
          output: '/storage/emulated/0/VisualLabStudio',
          executionTime: Date.now() - startTime,
        };

      case 'whoami':
        return {
          output: 'developer',
          executionTime: Date.now() - startTime,
        };

      case 'test':
        // Run tests asynchronously
        TestUtils.runAllTests()
          .then(result => {
            if (result.success) {
              addLine('output', '🎉 All tests passed!');
            } else {
              addLine('error', '❌ Some tests failed:');
            }
            result.results.forEach(testResult => {
              addLine('output', testResult);
            });
          })
          .catch(error => {
            addLine('error', `Test execution failed: ${error.message || error}`);
          });

        return {
          output: 'Running system tests...',
          executionTime: Date.now() - startTime,
        };

      case 'perf':
        // Run performance test asynchronously
        TestUtils.performanceTest()
          .then(result => {
            if (result.success) {
              addLine('output', `✅ Performance test: ${result.message}`);
            } else {
              addLine('error', `❌ Performance test failed: ${result.message}`);
            }
          })
          .catch(error => {
            addLine('error', `Performance test error: ${error.message || error}`);
          });

        return {
          output: 'Running performance test...',
          executionTime: Date.now() - startTime,
        };

      case 'memory':
        return {
          output: TestUtils.getMemoryUsage(),
          executionTime: Date.now() - startTime,
        };

      case 'samples':
        // Create sample files asynchronously
        TestUtils.createSampleFiles()
          .then(files => {
            addLine('output', `✅ Created ${files.length} sample files:`);
            files.forEach(file => {
              addLine('output', `  - ${file.name} (${file.language})`);
            });
          })
          .catch(error => {
            addLine('error', `Failed to create sample files: ${error.message || error}`);
          });

        return {
          output: 'Creating sample files...',
          executionTime: Date.now() - startTime,
        };

      default:
        return {
          output: '',
          error: `Command not found: ${cmd}\nType "help" for available commands`,
          executionTime: Date.now() - startTime,
        };
    }
  };

  const handleSubmit = () => {
    if (currentInput.trim() && !isExecuting) {
      executeCommand(currentInput);
      setCurrentInput('');
    }
  };

  const clearTerminal = () => {
    setLines([]);
    addLine('output', 'Terminal cleared');
  };

  const getLineColor = (type: 'input' | 'output' | 'error') => {
    switch (type) {
      case 'input':
        return theme.colors.primary;
      case 'error':
        return theme.colors.error;
      case 'output':
      default:
        return theme.colors.text;
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Terminal</Text>
        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: theme.colors.error }]}
          onPress={clearTerminal}
        >
          <Text style={[styles.clearButtonText, { color: theme.colors.background }]}>Clear</Text>
        </TouchableOpacity>
      </View>

      {/* Terminal Output */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.terminalOutput}
        contentContainerStyle={styles.terminalContent}
      >
        {lines.map((line) => (
          <Text
            key={line.id}
            style={[
              styles.terminalLine,
              {
                color: getLineColor(line.type),
                fontFamily: 'monospace',
                fontSize: theme.typography.fontSize.sm,
              }
            ]}
          >
            {line.content}
          </Text>
        ))}
        {isExecuting && (
          <Text style={[styles.terminalLine, { color: theme.colors.textSecondary }]}>
            Executing...
          </Text>
        )}
      </ScrollView>

      {/* Input Area */}
      <View style={[styles.inputContainer, { backgroundColor: theme.colors.surface, borderTopColor: theme.colors.border }]}>
        <Text style={[styles.prompt, { color: theme.colors.primary }]}>$</Text>
        <TextInput
          style={[
            styles.input,
            {
              color: theme.colors.text,
              fontSize: theme.typography.fontSize.sm,
            }
          ]}
          value={currentInput}
          onChangeText={setCurrentInput}
          onSubmitEditing={handleSubmit}
          placeholder="Enter command..."
          placeholderTextColor={theme.colors.textSecondary}
          autoCapitalize="none"
          autoCorrect={false}
          keyboardType="ascii-capable"
          editable={!isExecuting}
        />
        <TouchableOpacity
          style={[
            styles.submitButton,
            { backgroundColor: isExecuting ? theme.colors.textSecondary : theme.colors.primary }
          ]}
          onPress={handleSubmit}
          disabled={isExecuting || !currentInput.trim()}
        >
          <Text style={[styles.submitButtonText, { color: theme.colors.background }]}>Send</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  clearButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  terminalOutput: {
    flex: 1,
  },
  terminalContent: {
    padding: 16,
  },
  terminalLine: {
    lineHeight: 20,
    marginBottom: 2,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  prompt: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontFamily: 'monospace',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  submitButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 4,
  },
  submitButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
});

export default TerminalScreen;
