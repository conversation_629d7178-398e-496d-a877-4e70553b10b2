import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '../contexts/ThemeContext';
import { ExecutionResult } from '../types';

interface TerminalLine {
  id: string;
  type: 'input' | 'output' | 'error';
  content: string;
  timestamp: Date;
}

const TerminalScreen: React.FC = () => {
  const { theme } = useTheme();
  const [lines, setLines] = useState<TerminalLine[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    // Add welcome message
    addLine('output', 'Visual Lab Studio IDE Terminal v1.0.0');
    addLine('output', 'Type "help" for available commands');
    addLine('output', '');
  }, []);

  useEffect(() => {
    // Auto-scroll to bottom when new lines are added
    scrollViewRef.current?.scrollToEnd({ animated: true });
  }, [lines]);

  const addLine = (type: 'input' | 'output' | 'error', content: string) => {
    const newLine: TerminalLine = {
      id: `line-${Date.now()}-${Math.random()}`,
      type,
      content,
      timestamp: new Date(),
    };
    setLines(prev => [...prev, newLine]);
  };

  const executeCommand = async (command: string) => {
    if (!command.trim()) return;

    // Add input line
    addLine('input', `$ ${command}`);
    setIsExecuting(true);

    try {
      const result = await processCommand(command.trim());
      
      if (result.error) {
        addLine('error', result.error);
      } else if (result.output) {
        result.output.split('\n').forEach(line => {
          addLine('output', line);
        });
      }
    } catch (error) {
      addLine('error', `Error: ${error}`);
    } finally {
      setIsExecuting(false);
    }
  };

  const processCommand = async (command: string): Promise<ExecutionResult> => {
    const startTime = Date.now();
    
    // Parse command
    const parts = command.split(' ');
    const cmd = parts[0].toLowerCase();
    const args = parts.slice(1);

    switch (cmd) {
      case 'help':
        return {
          output: `Available commands:
  help          - Show this help message
  clear         - Clear terminal
  echo <text>   - Echo text
  date          - Show current date and time
  version       - Show IDE version
  run <lang>    - Run code (js, python)
  ls            - List files (coming soon)
  pwd           - Show current directory (coming soon)`,
          executionTime: Date.now() - startTime,
        };

      case 'clear':
        setLines([]);
        return {
          output: '',
          executionTime: Date.now() - startTime,
        };

      case 'echo':
        return {
          output: args.join(' '),
          executionTime: Date.now() - startTime,
        };

      case 'date':
        return {
          output: new Date().toString(),
          executionTime: Date.now() - startTime,
        };

      case 'version':
        return {
          output: 'Visual Lab Studio IDE v1.0.0\nReact Native Mobile IDE\nBuilt with ❤️ for developers',
          executionTime: Date.now() - startTime,
        };

      case 'run':
        const language = args[0];
        if (!language) {
          return {
            output: '',
            error: 'Usage: run <language>\nSupported: js, python',
            executionTime: Date.now() - startTime,
          };
        }
        
        // TODO: Implement code execution
        return {
          output: `Code execution for ${language} will be implemented soon!`,
          executionTime: Date.now() - startTime,
        };

      case 'ls':
        return {
          output: 'File listing will be implemented soon!',
          executionTime: Date.now() - startTime,
        };

      case 'pwd':
        return {
          output: '/storage/emulated/0/VisualLabStudio',
          executionTime: Date.now() - startTime,
        };

      default:
        return {
          output: '',
          error: `Command not found: ${cmd}\nType "help" for available commands`,
          executionTime: Date.now() - startTime,
        };
    }
  };

  const handleSubmit = () => {
    if (currentInput.trim() && !isExecuting) {
      executeCommand(currentInput);
      setCurrentInput('');
    }
  };

  const clearTerminal = () => {
    setLines([]);
    addLine('output', 'Terminal cleared');
  };

  const getLineColor = (type: 'input' | 'output' | 'error') => {
    switch (type) {
      case 'input':
        return theme.colors.primary;
      case 'error':
        return theme.colors.error;
      case 'output':
      default:
        return theme.colors.text;
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Terminal</Text>
        <TouchableOpacity
          style={[styles.clearButton, { backgroundColor: theme.colors.error }]}
          onPress={clearTerminal}
        >
          <Icon name="clear" size={20} color={theme.colors.background} />
        </TouchableOpacity>
      </View>

      {/* Terminal Output */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.terminalOutput}
        contentContainerStyle={styles.terminalContent}
      >
        {lines.map((line) => (
          <Text
            key={line.id}
            style={[
              styles.terminalLine,
              {
                color: getLineColor(line.type),
                fontFamily: 'monospace',
                fontSize: theme.typography.fontSize.sm,
              }
            ]}
          >
            {line.content}
          </Text>
        ))}
        {isExecuting && (
          <Text style={[styles.terminalLine, { color: theme.colors.textSecondary }]}>
            Executing...
          </Text>
        )}
      </ScrollView>

      {/* Input Area */}
      <View style={[styles.inputContainer, { backgroundColor: theme.colors.surface, borderTopColor: theme.colors.border }]}>
        <Text style={[styles.prompt, { color: theme.colors.primary }]}>$</Text>
        <TextInput
          style={[
            styles.input,
            {
              color: theme.colors.text,
              fontSize: theme.typography.fontSize.sm,
            }
          ]}
          value={currentInput}
          onChangeText={setCurrentInput}
          onSubmitEditing={handleSubmit}
          placeholder="Enter command..."
          placeholderTextColor={theme.colors.textSecondary}
          autoCapitalize="none"
          autoCorrect={false}
          keyboardType="ascii-capable"
          editable={!isExecuting}
        />
        <TouchableOpacity
          style={[
            styles.submitButton,
            { backgroundColor: isExecuting ? theme.colors.textSecondary : theme.colors.primary }
          ]}
          onPress={handleSubmit}
          disabled={isExecuting || !currentInput.trim()}
        >
          <Icon name="send" size={20} color={theme.colors.background} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  clearButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  terminalOutput: {
    flex: 1,
  },
  terminalContent: {
    padding: 16,
  },
  terminalLine: {
    lineHeight: 20,
    marginBottom: 2,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  prompt: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontFamily: 'monospace',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  submitButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TerminalScreen;
