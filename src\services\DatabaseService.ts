import SQLite from 'react-native-sqlite-2';
import { <PERSON><PERSON><PERSON><PERSON>, RecentFileEntity, ProjectEntity, UserSettingsEntity, CodeSnippetEntity } from '../types';

export class DatabaseService {
  private static db: any = null;
  private static readonly DB_NAME = 'visual_lab_studio.db';
  private static readonly DB_VERSION = '1.0';

  // Initialize database
  static async initialize(): Promise<void> {
    try {
      this.db = SQLite.openDatabase(
        this.DB_NAME,
        this.DB_VERSION,
        'Visual Lab Studio IDE Database',
        200000
      );

      await this.createTables();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Error initializing database:', error);
      throw error;
    }
  }

  // Create all tables
  private static async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const queries = [
      // Files table
      `CREATE TABLE IF NOT EXISTS files (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        path TEXT,
        content TEXT NOT NULL,
        language TEXT NOT NULL,
        size INTEGER NOT NULL,
        lastModified TEXT NOT NULL,
        isTemporary INTEGER NOT NULL DEFAULT 0,
        createdAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )`,

      // Recent files table
      `CREATE TABLE IF NOT EXISTS recent_files (
        id TEXT PRIMARY KEY,
        fileId TEXT NOT NULL,
        fileName TEXT NOT NULL,
        filePath TEXT,
        lastOpened TEXT NOT NULL,
        FOREIGN KEY (fileId) REFERENCES files (id) ON DELETE CASCADE
      )`,

      // Projects table
      `CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        path TEXT NOT NULL,
        description TEXT,
        createdAt TEXT NOT NULL,
        lastModified TEXT NOT NULL
      )`,

      // User settings table
      `CREATE TABLE IF NOT EXISTS user_settings (
        id TEXT PRIMARY KEY,
        theme TEXT NOT NULL DEFAULT 'dark',
        fontSize INTEGER NOT NULL DEFAULT 14,
        fontFamily TEXT NOT NULL DEFAULT 'monospace',
        autoSave INTEGER NOT NULL DEFAULT 1,
        wordWrap INTEGER NOT NULL DEFAULT 1,
        showLineNumbers INTEGER NOT NULL DEFAULT 1,
        tabSize INTEGER NOT NULL DEFAULT 2,
        defaultLanguage TEXT NOT NULL DEFAULT 'javascript'
      )`,

      // Code snippets table
      `CREATE TABLE IF NOT EXISTS code_snippets (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        language TEXT NOT NULL,
        tags TEXT,
        createdAt TEXT NOT NULL,
        lastModified TEXT NOT NULL
      )`,

      // Indexes for better performance
      `CREATE INDEX IF NOT EXISTS idx_files_name ON files (name)`,
      `CREATE INDEX IF NOT EXISTS idx_files_language ON files (language)`,
      `CREATE INDEX IF NOT EXISTS idx_recent_files_lastOpened ON recent_files (lastOpened DESC)`,
      `CREATE INDEX IF NOT EXISTS idx_projects_name ON projects (name)`,
      `CREATE INDEX IF NOT EXISTS idx_code_snippets_language ON code_snippets (language)`,
    ];

    for (const query of queries) {
      await this.executeQuery(query);
    }
  }

  // Execute SQL query
  private static executeQuery(query: string, params: any[] = []): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      this.db.transaction((tx: any) => {
        tx.executeSql(
          query,
          params,
          (_: any, result: any) => resolve(result),
          (_: any, error: any) => {
            console.error('SQL Error:', error);
            reject(error);
            return false;
          }
        );
      });
    });
  }

  // File operations
  static async saveFile(file: FileEntity): Promise<void> {
    const query = `
      INSERT OR REPLACE INTO files 
      (id, name, path, content, language, size, lastModified, isTemporary)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await this.executeQuery(query, [
      file.id,
      file.name,
      file.path,
      file.content,
      file.language,
      file.size,
      file.lastModified.toISOString(),
      file.isTemporary ? 1 : 0,
    ]);
  }

  static async getFileById(id: string): Promise<FileEntity | null> {
    const query = 'SELECT * FROM files WHERE id = ?';
    const result = await this.executeQuery(query, [id]);
    
    if (result.rows.length === 0) return null;
    
    const row = result.rows.item(0);
    return this.mapRowToFileEntity(row);
  }

  static async getAllFiles(): Promise<FileEntity[]> {
    const query = 'SELECT * FROM files ORDER BY lastModified DESC';
    const result = await this.executeQuery(query);
    
    const files: FileEntity[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      files.push(this.mapRowToFileEntity(result.rows.item(i)));
    }
    
    return files;
  }

  static async deleteFile(id: string): Promise<void> {
    const query = 'DELETE FROM files WHERE id = ?';
    await this.executeQuery(query, [id]);
  }

  static async searchFiles(searchTerm: string): Promise<FileEntity[]> {
    const query = `
      SELECT * FROM files 
      WHERE name LIKE ? OR content LIKE ? 
      ORDER BY lastModified DESC
    `;
    const searchPattern = `%${searchTerm}%`;
    const result = await this.executeQuery(query, [searchPattern, searchPattern]);
    
    const files: FileEntity[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      files.push(this.mapRowToFileEntity(result.rows.item(i)));
    }
    
    return files;
  }

  // Recent files operations
  static async addRecentFile(recentFile: RecentFileEntity): Promise<void> {
    // First, remove existing entry for this file
    await this.executeQuery('DELETE FROM recent_files WHERE fileId = ?', [recentFile.fileId]);
    
    // Then add new entry
    const query = `
      INSERT INTO recent_files (id, fileId, fileName, filePath, lastOpened)
      VALUES (?, ?, ?, ?, ?)
    `;
    
    await this.executeQuery(query, [
      recentFile.id,
      recentFile.fileId,
      recentFile.fileName,
      recentFile.filePath,
      recentFile.lastOpened.toISOString(),
    ]);

    // Keep only last 20 recent files
    await this.executeQuery(`
      DELETE FROM recent_files 
      WHERE id NOT IN (
        SELECT id FROM recent_files 
        ORDER BY lastOpened DESC 
        LIMIT 20
      )
    `);
  }

  static async getRecentFiles(): Promise<RecentFileEntity[]> {
    const query = 'SELECT * FROM recent_files ORDER BY lastOpened DESC LIMIT 20';
    const result = await this.executeQuery(query);
    
    const recentFiles: RecentFileEntity[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      recentFiles.push({
        id: row.id,
        fileId: row.fileId,
        fileName: row.fileName,
        filePath: row.filePath,
        lastOpened: new Date(row.lastOpened),
      });
    }
    
    return recentFiles;
  }

  // User settings operations
  static async saveUserSettings(settings: UserSettingsEntity): Promise<void> {
    const query = `
      INSERT OR REPLACE INTO user_settings 
      (id, theme, fontSize, fontFamily, autoSave, wordWrap, showLineNumbers, tabSize, defaultLanguage)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    await this.executeQuery(query, [
      settings.id,
      settings.theme,
      settings.fontSize,
      settings.fontFamily,
      settings.autoSave ? 1 : 0,
      settings.wordWrap ? 1 : 0,
      settings.showLineNumbers ? 1 : 0,
      settings.tabSize,
      settings.defaultLanguage,
    ]);
  }

  static async getUserSettings(): Promise<UserSettingsEntity | null> {
    const query = 'SELECT * FROM user_settings LIMIT 1';
    const result = await this.executeQuery(query);
    
    if (result.rows.length === 0) {
      // Return default settings
      const defaultSettings: UserSettingsEntity = {
        id: 'default',
        theme: 'dark',
        fontSize: 14,
        fontFamily: 'monospace',
        autoSave: true,
        wordWrap: true,
        showLineNumbers: true,
        tabSize: 2,
        defaultLanguage: 'javascript',
      };
      await this.saveUserSettings(defaultSettings);
      return defaultSettings;
    }
    
    const row = result.rows.item(0);
    return {
      id: row.id,
      theme: row.theme,
      fontSize: row.fontSize,
      fontFamily: row.fontFamily,
      autoSave: row.autoSave === 1,
      wordWrap: row.wordWrap === 1,
      showLineNumbers: row.showLineNumbers === 1,
      tabSize: row.tabSize,
      defaultLanguage: row.defaultLanguage,
    };
  }

  // Code snippets operations
  static async saveCodeSnippet(snippet: CodeSnippetEntity): Promise<void> {
    const query = `
      INSERT OR REPLACE INTO code_snippets 
      (id, title, content, language, tags, createdAt, lastModified)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    
    await this.executeQuery(query, [
      snippet.id,
      snippet.title,
      snippet.content,
      snippet.language,
      JSON.stringify(snippet.tags),
      snippet.createdAt.toISOString(),
      snippet.lastModified.toISOString(),
    ]);
  }

  static async getAllCodeSnippets(): Promise<CodeSnippetEntity[]> {
    const query = 'SELECT * FROM code_snippets ORDER BY lastModified DESC';
    const result = await this.executeQuery(query);
    
    const snippets: CodeSnippetEntity[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      snippets.push({
        id: row.id,
        title: row.title,
        content: row.content,
        language: row.language,
        tags: JSON.parse(row.tags || '[]'),
        createdAt: new Date(row.createdAt),
        lastModified: new Date(row.lastModified),
      });
    }
    
    return snippets;
  }

  // Helper method to map database row to FileEntity
  private static mapRowToFileEntity(row: any): FileEntity {
    return {
      id: row.id,
      name: row.name,
      path: row.path || '',
      content: row.content,
      language: row.language,
      size: row.size,
      lastModified: new Date(row.lastModified),
      isTemporary: row.isTemporary === 1,
    };
  }

  // Close database connection
  static close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}
