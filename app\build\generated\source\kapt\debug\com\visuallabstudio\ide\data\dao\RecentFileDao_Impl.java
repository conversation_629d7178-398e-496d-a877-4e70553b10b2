package com.visuallabstudio.ide.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.visuallabstudio.ide.data.database.Converters;
import com.visuallabstudio.ide.data.entities.RecentFileEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class RecentFileDao_Impl implements RecentFileDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<RecentFileEntity> __insertionAdapterOfRecentFileEntity;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<RecentFileEntity> __updateAdapterOfRecentFileEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteRecentFile;

  private final SharedSQLiteStatement __preparedStmtOfClearRecentFiles;

  public RecentFileDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfRecentFileEntity = new EntityInsertionAdapter<RecentFileEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `recent_files` (`id`,`fileId`,`fileName`,`filePath`,`lastOpened`,`openCount`) VALUES (?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final RecentFileEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getFileId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getFileId());
        }
        if (entity.getFileName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getFileName());
        }
        if (entity.getFilePath() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getFilePath());
        }
        final Long _tmp = __converters.dateToTimestamp(entity.getLastOpened());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp);
        }
        statement.bindLong(6, entity.getOpenCount());
      }
    };
    this.__updateAdapterOfRecentFileEntity = new EntityDeletionOrUpdateAdapter<RecentFileEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `recent_files` SET `id` = ?,`fileId` = ?,`fileName` = ?,`filePath` = ?,`lastOpened` = ?,`openCount` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final RecentFileEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getFileId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getFileId());
        }
        if (entity.getFileName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getFileName());
        }
        if (entity.getFilePath() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getFilePath());
        }
        final Long _tmp = __converters.dateToTimestamp(entity.getLastOpened());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp);
        }
        statement.bindLong(6, entity.getOpenCount());
        if (entity.getId() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteRecentFile = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM recent_files WHERE fileId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearRecentFiles = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM recent_files";
        return _query;
      }
    };
  }

  @Override
  public Object insertRecentFile(final RecentFileEntity recentFile,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfRecentFileEntity.insert(recentFile);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateRecentFile(final RecentFileEntity recentFile,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfRecentFileEntity.handle(recentFile);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteRecentFile(final String fileId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteRecentFile.acquire();
        int _argIndex = 1;
        if (fileId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, fileId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteRecentFile.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearRecentFiles(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearRecentFiles.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearRecentFiles.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<RecentFileEntity>> getRecentFiles() {
    final String _sql = "SELECT * FROM recent_files ORDER BY lastOpened DESC LIMIT 20";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"recent_files"}, new Callable<List<RecentFileEntity>>() {
      @Override
      @NonNull
      public List<RecentFileEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFileId = CursorUtil.getColumnIndexOrThrow(_cursor, "fileId");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfLastOpened = CursorUtil.getColumnIndexOrThrow(_cursor, "lastOpened");
          final int _cursorIndexOfOpenCount = CursorUtil.getColumnIndexOrThrow(_cursor, "openCount");
          final List<RecentFileEntity> _result = new ArrayList<RecentFileEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final RecentFileEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpFileId;
            if (_cursor.isNull(_cursorIndexOfFileId)) {
              _tmpFileId = null;
            } else {
              _tmpFileId = _cursor.getString(_cursorIndexOfFileId);
            }
            final String _tmpFileName;
            if (_cursor.isNull(_cursorIndexOfFileName)) {
              _tmpFileName = null;
            } else {
              _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            }
            final String _tmpFilePath;
            if (_cursor.isNull(_cursorIndexOfFilePath)) {
              _tmpFilePath = null;
            } else {
              _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            }
            final Date _tmpLastOpened;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastOpened)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastOpened);
            }
            _tmpLastOpened = __converters.fromTimestamp(_tmp);
            final int _tmpOpenCount;
            _tmpOpenCount = _cursor.getInt(_cursorIndexOfOpenCount);
            _item = new RecentFileEntity(_tmpId,_tmpFileId,_tmpFileName,_tmpFilePath,_tmpLastOpened,_tmpOpenCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getRecentFile(final String fileId,
      final Continuation<? super RecentFileEntity> $completion) {
    final String _sql = "SELECT * FROM recent_files WHERE fileId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (fileId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, fileId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<RecentFileEntity>() {
      @Override
      @Nullable
      public RecentFileEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFileId = CursorUtil.getColumnIndexOrThrow(_cursor, "fileId");
          final int _cursorIndexOfFileName = CursorUtil.getColumnIndexOrThrow(_cursor, "fileName");
          final int _cursorIndexOfFilePath = CursorUtil.getColumnIndexOrThrow(_cursor, "filePath");
          final int _cursorIndexOfLastOpened = CursorUtil.getColumnIndexOrThrow(_cursor, "lastOpened");
          final int _cursorIndexOfOpenCount = CursorUtil.getColumnIndexOrThrow(_cursor, "openCount");
          final RecentFileEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpFileId;
            if (_cursor.isNull(_cursorIndexOfFileId)) {
              _tmpFileId = null;
            } else {
              _tmpFileId = _cursor.getString(_cursorIndexOfFileId);
            }
            final String _tmpFileName;
            if (_cursor.isNull(_cursorIndexOfFileName)) {
              _tmpFileName = null;
            } else {
              _tmpFileName = _cursor.getString(_cursorIndexOfFileName);
            }
            final String _tmpFilePath;
            if (_cursor.isNull(_cursorIndexOfFilePath)) {
              _tmpFilePath = null;
            } else {
              _tmpFilePath = _cursor.getString(_cursorIndexOfFilePath);
            }
            final Date _tmpLastOpened;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastOpened)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastOpened);
            }
            _tmpLastOpened = __converters.fromTimestamp(_tmp);
            final int _tmpOpenCount;
            _tmpOpenCount = _cursor.getInt(_cursorIndexOfOpenCount);
            _result = new RecentFileEntity(_tmpId,_tmpFileId,_tmpFileName,_tmpFilePath,_tmpLastOpened,_tmpOpenCount);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
