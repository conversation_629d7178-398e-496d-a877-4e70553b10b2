{"logs": [{"outputFile": "com.visuallabstudio.ide.app-mergeDebugResources-53:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3fdae449ec0975b3ed67992fb9c55c3c\\transformed\\jetified-ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,989,1053,1136,1224,1298,1377,1443", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,984,1048,1131,1219,1293,1372,1438,1559"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1272,1367,5161,5258,5437,5600,5682,5778,5867,5954,6018,6082,6165,6337,6692,6771,6837", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "1362,1450,5253,5352,5519,5677,5773,5862,5949,6013,6077,6160,6248,6406,6766,6832,6953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f063da582707269a21bd980d1f8ebd44\\transformed\\jetified-material3-1.1.2\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,284,388,500,580,673,784,920,1039,1182,1263,1359,1453,1551,1669,1802,1903,2039,2173,2295,2487,2606,2724,2843,2974,3069,3160,3275,3399,3497,3602,3708,3848,3991,4094,4206,4286,4362,4446,4529,4626,4703,4782,4877,4979,5070,5162,5244,5349,5443,5537,5673,5750,5858", "endColumns": "116,111,103,111,79,92,110,135,118,142,80,95,93,97,117,132,100,135,133,121,191,118,117,118,130,94,90,114,123,97,104,105,139,142,102,111,79,75,83,82,96,76,78,94,101,90,91,81,104,93,93,135,76,107,93", "endOffsets": "167,279,383,495,575,668,779,915,1034,1177,1258,1354,1448,1546,1664,1797,1898,2034,2168,2290,2482,2601,2719,2838,2969,3064,3155,3270,3394,3492,3597,3703,3843,3986,4089,4201,4281,4357,4441,4524,4621,4698,4777,4872,4974,5065,5157,5239,5344,5438,5532,5668,5745,5853,5947"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,438,1455,1535,1628,1739,1875,1994,2137,2218,2314,2408,2506,2624,2757,2858,2994,3128,3250,3442,3561,3679,3798,3929,4024,4115,4230,4354,4452,4557,4663,4803,4946,5049,5357,5524,6253,6411,6595,6958,7035,7114,7209,7311,7402,7494,7576,7681,7775,7869,8005,8082,8190", "endColumns": "116,111,103,111,79,92,110,135,118,142,80,95,93,97,117,132,100,135,133,121,191,118,117,118,130,94,90,114,123,97,104,105,139,142,102,111,79,75,83,82,96,76,78,94,101,90,91,81,104,93,93,135,76,107,93", "endOffsets": "217,329,433,545,1530,1623,1734,1870,1989,2132,2213,2309,2403,2501,2619,2752,2853,2989,3123,3245,3437,3556,3674,3793,3924,4019,4110,4225,4349,4447,4552,4658,4798,4941,5044,5156,5432,5595,6332,6489,6687,7030,7109,7204,7306,7397,7489,7571,7676,7770,7864,8000,8077,8185,8279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8894ffd5200146a245d974f5f73061d6\\transformed\\core-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "550,647,749,851,952,1055,1162,6494", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "642,744,846,947,1050,1157,1267,6590"}}]}]}