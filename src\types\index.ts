export interface FileEntity {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  size: number;
  lastModified: Date;
  isTemporary: boolean;
}

export interface RecentFileEntity {
  id: string;
  fileId: string;
  fileName: string;
  filePath: string;
  lastOpened: Date;
}

export interface ProjectEntity {
  id: string;
  name: string;
  path: string;
  description?: string;
  createdAt: Date;
  lastModified: Date;
}

export interface UserSettingsEntity {
  id: string;
  theme: 'light' | 'dark';
  fontSize: number;
  fontFamily: string;
  autoSave: boolean;
  wordWrap: boolean;
  showLineNumbers: boolean;
  tabSize: number;
  defaultLanguage: string;
}

export interface CodeSnippetEntity {
  id: string;
  title: string;
  content: string;
  language: string;
  tags: string[];
  createdAt: Date;
  lastModified: Date;
}

export type Language = 'javascript' | 'python' | 'html' | 'css' | 'json' | 'markdown' | 'text';

export interface ExecutionResult {
  output: string;
  error?: string;
  executionTime: number;
}

export interface TabItem {
  title: string;
  icon: string;
}

export interface NavigationState {
  currentTab: number;
  tabs: TabItem[];
}
