export default {
    "hljs-comment": {
        "color": "#687d68"
    },
    "hljs-quote": {
        "color": "#687d68"
    },
    "hljs-variable": {
        "color": "#e6193c"
    },
    "hljs-template-variable": {
        "color": "#e6193c"
    },
    "hljs-attribute": {
        "color": "#e6193c"
    },
    "hljs-tag": {
        "color": "#e6193c"
    },
    "hljs-name": {
        "color": "#e6193c"
    },
    "hljs-regexp": {
        "color": "#e6193c"
    },
    "hljs-link": {
        "color": "#e6193c"
    },
    "hljs-selector-id": {
        "color": "#e6193c"
    },
    "hljs-selector-class": {
        "color": "#e6193c"
    },
    "hljs-number": {
        "color": "#87711d"
    },
    "hljs-meta": {
        "color": "#87711d"
    },
    "hljs-built_in": {
        "color": "#87711d"
    },
    "hljs-builtin-name": {
        "color": "#87711d"
    },
    "hljs-literal": {
        "color": "#87711d"
    },
    "hljs-type": {
        "color": "#87711d"
    },
    "hljs-params": {
        "color": "#87711d"
    },
    "hljs-string": {
        "color": "#29a329"
    },
    "hljs-symbol": {
        "color": "#29a329"
    },
    "hljs-bullet": {
        "color": "#29a329"
    },
    "hljs-title": {
        "color": "#3d62f5"
    },
    "hljs-section": {
        "color": "#3d62f5"
    },
    "hljs-keyword": {
        "color": "#ad2bee"
    },
    "hljs-selector-tag": {
        "color": "#ad2bee"
    },
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "background": "#f4fbf4",
        "color": "#5e6e5e",
        "padding": "0.5em"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}