export default {
    "hljs-comment": {
        "color": "#7195a8"
    },
    "hljs-quote": {
        "color": "#7195a8"
    },
    "hljs-variable": {
        "color": "#d22d72"
    },
    "hljs-template-variable": {
        "color": "#d22d72"
    },
    "hljs-attribute": {
        "color": "#d22d72"
    },
    "hljs-tag": {
        "color": "#d22d72"
    },
    "hljs-name": {
        "color": "#d22d72"
    },
    "hljs-regexp": {
        "color": "#d22d72"
    },
    "hljs-link": {
        "color": "#d22d72"
    },
    "hljs-selector-id": {
        "color": "#d22d72"
    },
    "hljs-selector-class": {
        "color": "#d22d72"
    },
    "hljs-number": {
        "color": "#935c25"
    },
    "hljs-meta": {
        "color": "#935c25"
    },
    "hljs-built_in": {
        "color": "#935c25"
    },
    "hljs-builtin-name": {
        "color": "#935c25"
    },
    "hljs-literal": {
        "color": "#935c25"
    },
    "hljs-type": {
        "color": "#935c25"
    },
    "hljs-params": {
        "color": "#935c25"
    },
    "hljs-string": {
        "color": "#568c3b"
    },
    "hljs-symbol": {
        "color": "#568c3b"
    },
    "hljs-bullet": {
        "color": "#568c3b"
    },
    "hljs-title": {
        "color": "#257fad"
    },
    "hljs-section": {
        "color": "#257fad"
    },
    "hljs-keyword": {
        "color": "#6b6bb8"
    },
    "hljs-selector-tag": {
        "color": "#6b6bb8"
    },
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "background": "#161b1d",
        "color": "#7ea2b4",
        "padding": "0.5em"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}