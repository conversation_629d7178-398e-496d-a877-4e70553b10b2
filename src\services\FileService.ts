import RNFS from 'react-native-fs';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { DatabaseService } from './DatabaseService';
import { FileEntity, Language } from '../types';

export class FileService {
  private static readonly STORAGE_KEY = 'visual_lab_files';
  private static readonly RECENT_FILES_KEY = 'recent_files';

  // Get app's document directory
  static getDocumentDirectory(): string {
    return RNFS.DocumentDirectoryPath;
  }

  // Get app's cache directory
  static getCacheDirectory(): string {
    return RNFS.CachesDirectoryPath;
  }

  // Create a new file
  static async createFile(name: string, content: string = '', language?: Language): Promise<FileEntity> {
    const id = `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const detectedLanguage = language || this.detectLanguageFromExtension(name);
    
    const file: FileEntity = {
      id,
      name,
      path: '',
      content,
      language: detectedLanguage,
      size: content.length,
      lastModified: new Date(),
      isTemporary: true,
    };

    await this.saveFileToStorage(file);
    return file;
  }

  // Save file to device storage
  static async saveFileToDevice(file: FileEntity, directoryPath?: string): Promise<string> {
    try {
      const basePath = directoryPath || this.getDocumentDirectory();
      const filePath = `${basePath}/VisualLabStudio/${file.name}`;
      
      // Ensure directory exists
      const dirPath = `${basePath}/VisualLabStudio`;
      const dirExists = await RNFS.exists(dirPath);
      if (!dirExists) {
        await RNFS.mkdir(dirPath);
      }

      // Write file
      await RNFS.writeFile(filePath, file.content, 'utf8');
      
      // Update file entity
      const updatedFile: FileEntity = {
        ...file,
        path: filePath,
        isTemporary: false,
        lastModified: new Date(),
        size: file.content.length,
      };

      await this.saveFileToStorage(updatedFile);
      await this.addToRecentFiles(updatedFile);
      
      return filePath;
    } catch (error) {
      console.error('Error saving file to device:', error);
      throw new Error(`Failed to save file: ${error}`);
    }
  }

  // Read file from device
  static async readFileFromDevice(filePath: string): Promise<string> {
    try {
      const exists = await RNFS.exists(filePath);
      if (!exists) {
        throw new Error('File does not exist');
      }

      const content = await RNFS.readFile(filePath, 'utf8');
      return content;
    } catch (error) {
      console.error('Error reading file from device:', error);
      throw new Error(`Failed to read file: ${error}`);
    }
  }

  // Load file from device and create FileEntity
  static async loadFileFromDevice(filePath: string): Promise<FileEntity> {
    try {
      const content = await this.readFileFromDevice(filePath);
      const stat = await RNFS.stat(filePath);
      const fileName = filePath.split('/').pop() || 'unknown';
      
      const file: FileEntity = {
        id: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: fileName,
        path: filePath,
        content,
        language: this.detectLanguageFromExtension(fileName),
        size: stat.size,
        lastModified: new Date(stat.mtime),
        isTemporary: false,
      };

      await this.saveFileToStorage(file);
      await this.addToRecentFiles(file);
      
      return file;
    } catch (error) {
      console.error('Error loading file from device:', error);
      throw new Error(`Failed to load file: ${error}`);
    }
  }

  // Save file metadata to database
  static async saveFileToStorage(file: FileEntity): Promise<void> {
    try {
      await DatabaseService.saveFile(file);
    } catch (error) {
      console.error('Error saving file to storage:', error);
      throw new Error(`Failed to save file metadata: ${error}`);
    }
  }

  // Get all files from database
  static async getAllFiles(): Promise<FileEntity[]> {
    try {
      return await DatabaseService.getAllFiles();
    } catch (error) {
      console.error('Error getting all files:', error);
      return [];
    }
  }

  // Get file by ID
  static async getFileById(id: string): Promise<FileEntity | null> {
    try {
      return await DatabaseService.getFileById(id);
    } catch (error) {
      console.error('Error getting file by ID:', error);
      return null;
    }
  }

  // Update file content
  static async updateFileContent(id: string, content: string): Promise<FileEntity | null> {
    try {
      const file = await this.getFileById(id);
      if (!file) return null;

      const updatedFile: FileEntity = {
        ...file,
        content,
        size: content.length,
        lastModified: new Date(),
      };

      // If file has a path, update the device file too
      if (updatedFile.path && !updatedFile.isTemporary) {
        await RNFS.writeFile(updatedFile.path, content, 'utf8');
      }

      await this.saveFileToStorage(updatedFile);
      await this.addToRecentFiles(updatedFile);
      
      return updatedFile;
    } catch (error) {
      console.error('Error updating file content:', error);
      throw new Error(`Failed to update file: ${error}`);
    }
  }

  // Delete file
  static async deleteFile(id: string): Promise<boolean> {
    try {
      const file = await this.getFileById(id);
      if (!file) return false;

      // Delete from device if it exists
      if (file.path && !file.isTemporary) {
        const exists = await RNFS.exists(file.path);
        if (exists) {
          await RNFS.unlink(file.path);
        }
      }

      // Remove from database
      await DatabaseService.deleteFile(id);

      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }

  // Add file to recent files
  static async addToRecentFiles(file: FileEntity): Promise<void> {
    try {
      const recentFile = {
        id: `recent_${Date.now()}`,
        fileId: file.id,
        fileName: file.name,
        filePath: file.path,
        lastOpened: new Date(),
      };

      await DatabaseService.addRecentFile(recentFile);
    } catch (error) {
      console.error('Error adding to recent files:', error);
    }
  }

  // Get recent files
  static async getRecentFiles() {
    try {
      return await DatabaseService.getRecentFiles();
    } catch (error) {
      console.error('Error getting recent files:', error);
      return [];
    }
  }

  // Detect language from file extension
  static detectLanguageFromExtension(fileName: string): Language {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return 'javascript';
      case 'py':
        return 'python';
      case 'html':
      case 'htm':
        return 'html';
      case 'css':
      case 'scss':
      case 'sass':
        return 'css';
      case 'json':
        return 'json';
      case 'md':
      case 'markdown':
        return 'markdown';
      default:
        return 'text';
    }
  }

  // List files in directory
  static async listDirectory(directoryPath: string): Promise<string[]> {
    try {
      const exists = await RNFS.exists(directoryPath);
      if (!exists) {
        return [];
      }

      const items = await RNFS.readDir(directoryPath);
      return items.map(item => item.path);
    } catch (error) {
      console.error('Error listing directory:', error);
      return [];
    }
  }

  // Get file stats
  static async getFileStats(filePath: string) {
    try {
      return await RNFS.stat(filePath);
    } catch (error) {
      console.error('Error getting file stats:', error);
      return null;
    }
  }
}
