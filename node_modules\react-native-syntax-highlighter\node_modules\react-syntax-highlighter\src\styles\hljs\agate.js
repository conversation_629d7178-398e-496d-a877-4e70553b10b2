export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#333",
        "color": "white"
    },
    "hljs-name": {
        "fontWeight": "bold"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    },
    "hljs-code": {
        "fontStyle": "italic",
        "color": "#888"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-tag": {
        "color": "#62c8f3"
    },
    "hljs-variable": {
        "color": "#ade5fc"
    },
    "hljs-template-variable": {
        "color": "#ade5fc"
    },
    "hljs-selector-id": {
        "color": "#ade5fc"
    },
    "hljs-selector-class": {
        "color": "#ade5fc"
    },
    "hljs-string": {
        "color": "#a2fca2"
    },
    "hljs-bullet": {
        "color": "#d36363"
    },
    "hljs-type": {
        "color": "#ffa"
    },
    "hljs-title": {
        "color": "#ffa"
    },
    "hljs-section": {
        "color": "#ffa"
    },
    "hljs-attribute": {
        "color": "#ffa"
    },
    "hljs-quote": {
        "color": "#ffa"
    },
    "hljs-built_in": {
        "color": "#ffa"
    },
    "hljs-builtin-name": {
        "color": "#ffa"
    },
    "hljs-number": {
        "color": "#d36363"
    },
    "hljs-symbol": {
        "color": "#d36363"
    },
    "hljs-keyword": {
        "color": "#fcc28c"
    },
    "hljs-selector-tag": {
        "color": "#fcc28c"
    },
    "hljs-literal": {
        "color": "#fcc28c"
    },
    "hljs-comment": {
        "color": "#888"
    },
    "hljs-deletion": {
        "color": "#333",
        "backgroundColor": "#fc9b9b"
    },
    "hljs-regexp": {
        "color": "#c6b4f0"
    },
    "hljs-link": {
        "color": "#c6b4f0"
    },
    "hljs-meta": {
        "color": "#fc9b9b"
    },
    "hljs-addition": {
        "backgroundColor": "#a2fca2",
        "color": "#333"
    }
}