import { useState, useCallback } from 'react';
import { FileService } from '../services/FileService';
import { FileEntity, RecentFileEntity, Language } from '../types';

export interface UseFileSystemReturn {
  // State
  files: FileEntity[];
  recentFiles: RecentFileEntity[];
  currentFile: FileEntity | null;
  isLoading: boolean;
  error: string | null;

  // File operations
  createFile: (name: string, content?: string, language?: Language) => Promise<FileEntity | null>;
  loadFile: (id: string) => Promise<FileEntity | null>;
  saveFile: (file: FileEntity, directoryPath?: string) => Promise<string | null>;
  updateFileContent: (id: string, content: string) => Promise<FileEntity | null>;
  deleteFile: (id: string) => Promise<boolean>;
  
  // File system operations
  loadFileFromDevice: (filePath: string) => Promise<FileEntity | null>;
  saveFileToDevice: (file: FileEntity, directoryPath?: string) => Promise<string | null>;
  
  // Data loading
  refreshFiles: () => Promise<void>;
  refreshRecentFiles: () => Promise<void>;
  
  // Utility
  setCurrentFile: (file: FileEntity | null) => void;
  clearError: () => void;
}

export const useFileSystem = (): UseFileSystemReturn => {
  const [files, setFiles] = useState<FileEntity[]>([]);
  const [recentFiles, setRecentFiles] = useState<RecentFileEntity[]>([]);
  const [currentFile, setCurrentFile] = useState<FileEntity | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleError = useCallback((error: any, operation: string) => {
    console.error(`Error in ${operation}:`, error);
    setError(`Failed to ${operation}: ${error.message || error}`);
    setIsLoading(false);
  }, []);

  const refreshFiles = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const allFiles = await FileService.getAllFiles();
      setFiles(allFiles);
    } catch (error) {
      handleError(error, 'load files');
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);

  const refreshRecentFiles = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const recent = await FileService.getRecentFiles();
      setRecentFiles(recent);
    } catch (error) {
      handleError(error, 'load recent files');
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);

  const createFile = useCallback(async (name: string, content = '', language?: Language): Promise<FileEntity | null> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const newFile = await FileService.createFile(name, content, language);
      await refreshFiles();
      setCurrentFile(newFile);
      
      return newFile;
    } catch (error) {
      handleError(error, 'create file');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError, refreshFiles]);

  const loadFile = useCallback(async (id: string): Promise<FileEntity | null> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const file = await FileService.getFileById(id);
      if (file) {
        await FileService.addToRecentFiles(file);
        await refreshRecentFiles();
        setCurrentFile(file);
      }
      
      return file;
    } catch (error) {
      handleError(error, 'load file');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError, refreshRecentFiles]);

  const saveFile = useCallback(async (file: FileEntity, directoryPath?: string): Promise<string | null> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const filePath = await FileService.saveFileToDevice(file, directoryPath);
      await refreshFiles();
      await refreshRecentFiles();
      
      // Update current file if it's the same
      if (currentFile?.id === file.id) {
        const updatedFile = await FileService.getFileById(file.id);
        setCurrentFile(updatedFile);
      }
      
      return filePath;
    } catch (error) {
      handleError(error, 'save file');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError, refreshFiles, refreshRecentFiles, currentFile]);

  const updateFileContent = useCallback(async (id: string, content: string): Promise<FileEntity | null> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const updatedFile = await FileService.updateFileContent(id, content);
      if (updatedFile) {
        await refreshFiles();
        await refreshRecentFiles();
        
        // Update current file if it's the same
        if (currentFile?.id === id) {
          setCurrentFile(updatedFile);
        }
      }
      
      return updatedFile;
    } catch (error) {
      handleError(error, 'update file');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError, refreshFiles, refreshRecentFiles, currentFile]);

  const deleteFile = useCallback(async (id: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const success = await FileService.deleteFile(id);
      if (success) {
        await refreshFiles();
        
        // Clear current file if it was deleted
        if (currentFile?.id === id) {
          setCurrentFile(null);
        }
      }
      
      return success;
    } catch (error) {
      handleError(error, 'delete file');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [handleError, refreshFiles, currentFile]);

  const loadFileFromDevice = useCallback(async (filePath: string): Promise<FileEntity | null> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const file = await FileService.loadFileFromDevice(filePath);
      await refreshFiles();
      await refreshRecentFiles();
      setCurrentFile(file);
      
      return file;
    } catch (error) {
      handleError(error, 'load file from device');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError, refreshFiles, refreshRecentFiles]);

  const saveFileToDevice = useCallback(async (file: FileEntity, directoryPath?: string): Promise<string | null> => {
    return saveFile(file, directoryPath);
  }, [saveFile]);

  return {
    // State
    files,
    recentFiles,
    currentFile,
    isLoading,
    error,

    // File operations
    createFile,
    loadFile,
    saveFile,
    updateFileContent,
    deleteFile,
    
    // File system operations
    loadFileFromDevice,
    saveFileToDevice,
    
    // Data loading
    refreshFiles,
    refreshRecentFiles,
    
    // Utility
    setCurrentFile,
    clearError,
  };
};
