{"logs": [{"outputFile": "com.visuallabstudio.ide.app-mergeDebugResources-53:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\3fdae449ec0975b3ed67992fb9c55c3c\\transformed\\jetified-ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,372,470,555,636,742,826,907,978,1045,1126,1209,1279,1355,1429", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "189,272,367,465,550,631,737,821,902,973,1040,1121,1204,1274,1350,1424,1545"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1262,1351,5062,5157,5331,5490,5571,5677,5761,5842,5913,5980,6061,6224,6573,6649,6723", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "1346,1429,5152,5250,5411,5566,5672,5756,5837,5908,5975,6056,6139,6289,6644,6718,6839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8894ffd5200146a245d974f5f73061d6\\transformed\\core-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "550,643,745,840,943,1046,1148,6375", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "638,740,835,938,1041,1143,1257,6471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f063da582707269a21bd980d1f8ebd44\\transformed\\jetified-material3-1.1.2\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,388,500,575,676,797,931,1049,1188,1271,1372,1460,1556,1669,1791,1897,2036,2173,2306,2454,2576,2692,2812,2927,3016,3110,3229,3349,3445,3544,3648,3785,3928,4031,4128,4204,4278,4358,4439,4536,4611,4691,4788,4887,4982,5078,5161,5263,5359,5457,5591,5666,5763", "endColumns": "113,112,105,111,74,100,120,133,117,138,82,100,87,95,112,121,105,138,136,132,147,121,115,119,114,88,93,118,119,95,98,103,136,142,102,96,75,73,79,80,96,74,79,96,98,94,95,82,101,95,97,133,74,96,88", "endOffsets": "164,277,383,495,570,671,792,926,1044,1183,1266,1367,1455,1551,1664,1786,1892,2031,2168,2301,2449,2571,2687,2807,2922,3011,3105,3224,3344,3440,3539,3643,3780,3923,4026,4123,4199,4273,4353,4434,4531,4606,4686,4783,4882,4977,5073,5156,5258,5354,5452,5586,5661,5758,5847"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,332,438,1434,1509,1610,1731,1865,1983,2122,2205,2306,2394,2490,2603,2725,2831,2970,3107,3240,3388,3510,3626,3746,3861,3950,4044,4163,4283,4379,4478,4582,4719,4862,4965,5255,5416,6144,6294,6476,6844,6919,6999,7096,7195,7290,7386,7469,7571,7667,7765,7899,7974,8071", "endColumns": "113,112,105,111,74,100,120,133,117,138,82,100,87,95,112,121,105,138,136,132,147,121,115,119,114,88,93,118,119,95,98,103,136,142,102,96,75,73,79,80,96,74,79,96,98,94,95,82,101,95,97,133,74,96,88", "endOffsets": "214,327,433,545,1504,1605,1726,1860,1978,2117,2200,2301,2389,2485,2598,2720,2826,2965,3102,3235,3383,3505,3621,3741,3856,3945,4039,4158,4278,4374,4473,4577,4714,4857,4960,5057,5326,5485,6219,6370,6568,6914,6994,7091,7190,7285,7381,7464,7566,7662,7760,7894,7969,8066,8155"}}]}]}