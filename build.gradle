// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        compose_version = '1.5.4'  // Versão original que funcionava
        kotlin_version = '1.9.20'  // Versão compatível com Compose 1.5.4
        room_version = '2.6.1'
        chaquopy_version = '15.0.1'  // Versão mais atual compatível
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.7.2'  // Compatível com Gradle 8.14.3
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.chaquo.python:gradle:$chaquopy_version"
    }
}

plugins {
    id 'com.android.application' version '8.7.2' apply false
    id 'com.android.library' version '8.7.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.20' apply false
}

allprojects {
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions {
            freeCompilerArgs += ["-Xsuppress-version-warnings"]
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
