#Tue Jul 15 21:43:45 BRT 2025
com.visuallabstudio.ide.app-main-57\:/drawable/ic_launcher_background.xml=C\:\\Users\\Administrator\\Documents\\AI Projects\\Visual Lab Studio Mobile\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.visuallabstudio.ide.app-main-57\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\Administrator\\Documents\\AI Projects\\Visual Lab Studio Mobile\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.visuallabstudio.ide.app-main-57\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\Administrator\\Documents\\AI Projects\\Visual Lab Studio Mobile\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.visuallabstudio.ide.app-main-57\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\Administrator\\Documents\\AI Projects\\Visual Lab Studio Mobile\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.visuallabstudio.ide.app-main-57\:/xml/backup_rules.xml=C\:\\Users\\Administrator\\Documents\\AI Projects\\Visual Lab Studio Mobile\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.visuallabstudio.ide.app-main-57\:/xml/data_extraction_rules.xml=C\:\\Users\\Administrator\\Documents\\AI Projects\\Visual Lab Studio Mobile\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
